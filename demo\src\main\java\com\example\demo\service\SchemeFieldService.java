package com.example.demo.service;

import com.example.demo.model.ApplicationFieldValue;
import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeApplication;
import com.example.demo.model.SchemeField;
import com.example.demo.repository.ApplicationFieldValueRepository;
import com.example.demo.repository.SchemeFieldRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Service
public class SchemeFieldService {

    @Autowired
    private SchemeFieldRepository schemeFieldRepository;

    @Autowired
    private ApplicationFieldValueRepository applicationFieldValueRepository;

    public List<SchemeField> getActiveFieldsByScheme(Scheme scheme) {
        return schemeFieldRepository.findBySchemeAndActiveOrderByDisplayOrder(scheme, true);
    }

    public List<SchemeField> getActiveFieldsBySchemeId(Long schemeId) {
        return schemeFieldRepository.findActiveFieldsBySchemeId(schemeId);
    }

    public SchemeField saveField(SchemeField field) {
        return schemeFieldRepository.save(field);
    }

    public Optional<SchemeField> getFieldById(Long id) {
        return schemeFieldRepository.findById(id);
    }

    public void deleteField(Long id) {
        schemeFieldRepository.deleteById(id);
    }

    @Transactional
    public void createDefaultFields(Scheme scheme) {
        // Create some default fields that are commonly needed
        createField(scheme, "applicant_name", "Full Name", "TEXT", "Enter your full name", true, 1);
        createField(scheme, "applicant_email", "Email Address", "EMAIL", "Enter your email address", true, 2);
        createField(scheme, "applicant_phone", "Phone Number", "PHONE", "Enter your phone number", true, 3);
        createField(scheme, "date_of_birth", "Date of Birth", "DATE", "Select your date of birth", true, 4);
        createField(scheme, "address", "Address", "TEXTAREA", "Enter your complete address", true, 5);
    }

    private void createField(Scheme scheme, String fieldName, String fieldLabel, String fieldType, 
                           String placeholder, boolean required, int displayOrder) {
        SchemeField field = new SchemeField(scheme, fieldName, fieldLabel, fieldType, required);
        field.setPlaceholder(placeholder);
        field.setDisplayOrder(displayOrder);
        schemeFieldRepository.save(field);
    }

    @Transactional
    public void saveApplicationFieldValues(SchemeApplication application, Map<String, String> fieldValues) {
        List<SchemeField> fields = getActiveFieldsByScheme(application.getScheme());
        
        for (SchemeField field : fields) {
            String value = fieldValues.get(field.getFieldName());
            if (value != null && !value.trim().isEmpty()) {
                // Check if value already exists
                Optional<ApplicationFieldValue> existingValue = 
                    applicationFieldValueRepository.findByApplicationAndSchemeField(application, field);
                
                if (existingValue.isPresent()) {
                    // Update existing value
                    existingValue.get().setFieldValue(value);
                    applicationFieldValueRepository.save(existingValue.get());
                } else {
                    // Create new value
                    ApplicationFieldValue fieldValue = new ApplicationFieldValue(application, field, value);
                    applicationFieldValueRepository.save(fieldValue);
                }
            }
        }
    }

    public List<ApplicationFieldValue> getApplicationFieldValues(SchemeApplication application) {
        return applicationFieldValueRepository.findByApplicationIdOrderByFieldOrder(application.getId());
    }

    public Map<String, String> getApplicationFieldValuesAsMap(SchemeApplication application) {
        List<ApplicationFieldValue> values = getApplicationFieldValues(application);
        return values.stream().collect(
            java.util.stream.Collectors.toMap(
                v -> v.getSchemeField().getFieldName(),
                ApplicationFieldValue::getFieldValue
            )
        );
    }

    @Transactional
    public void deleteFieldsByScheme(Scheme scheme) {
        schemeFieldRepository.deleteByScheme(scheme);
    }

    public long countActiveFieldsByScheme(Scheme scheme) {
        return schemeFieldRepository.countBySchemeAndActive(scheme, true);
    }

    public boolean isFieldNameDuplicate(Scheme scheme, String fieldName) {
        return schemeFieldRepository.findBySchemeAndFieldName(scheme, fieldName).isPresent();
    }

    public List<String> validateFieldNames(Scheme scheme, List<String> fieldNames) {
        List<String> errors = new ArrayList<>();
        Set<String> seenNames = new HashSet<>();

        for (String fieldName : fieldNames) {
            if (fieldName == null || fieldName.trim().isEmpty()) {
                continue;
            }

            String normalizedName = fieldName.trim().toLowerCase();

            // Check for duplicates within the current submission
            if (seenNames.contains(normalizedName)) {
                errors.add("Duplicate field name: '" + fieldName + "'. Each field must have a unique name.");
            } else {
                seenNames.add(normalizedName);

                // Check if field name already exists in the scheme
                if (isFieldNameDuplicate(scheme, fieldName)) {
                    errors.add("Field name '" + fieldName + "' already exists for this scheme. Please use a different name.");
                }
            }
        }

        return errors;
    }
}
