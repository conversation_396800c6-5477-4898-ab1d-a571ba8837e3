package com.example.demo.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "scheme_applications")
public class SchemeApplication {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "user_id", nullable = false)
    private GeneralUser user;

    @ManyToOne
    @JoinColumn(name = "scheme_id", nullable = false)
    private Scheme scheme;

    @Column(nullable = false)
    private LocalDateTime applicationDate;

    @Column(nullable = false)
    private String status; // PENDING, APPROVED, REJECTED, COMPLETED, CONFIRMED, BOND_BROKEN

    @Column
    private String remarks;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] supportingDocument;

    @Column
    private String documentContentType;

    @OneToMany(mappedBy = "application", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ApplicationDocument> applicationDocuments = new ArrayList<>();

    @Column
    private String paymentStatus; // PENDING, COMPLETED, VERIFIED

    @Column
    private String transactionId;

    @Lob
    @Column(columnDefinition = "LONGBLOB")
    private byte[] paymentScreenshot;

    @Column
    private String paymentScreenshotContentType;

    @Column
    private Boolean documentAccessGranted = false;

    @Column
    private LocalDateTime documentAccessGrantedDate;

    @Column
    private Boolean bondBroken = false;

    @Column
    private LocalDateTime bondBrokenDate;

    @Column
    private String bondBreakReason;

    @Column
    private Boolean adminVerified = false;

    @Column
    private LocalDateTime adminVerificationDate;

    @Column
    private String adminVerificationRemarks;

    @Column
    private Long adminVerifiedBy; // Admin ID who verified

    // Default constructor
    public SchemeApplication() {
        this.applicationDate = LocalDateTime.now();
        this.status = "PENDING";
        this.paymentStatus = "PENDING";
        this.adminVerified = false;
    }

    // Constructor with fields
    public SchemeApplication(GeneralUser user, Scheme scheme) {
        this.user = user;
        this.scheme = scheme;
        this.applicationDate = LocalDateTime.now();
        this.status = "PENDING";
        this.paymentStatus = "PENDING";
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public GeneralUser getUser() {
        return user;
    }

    public void setUser(GeneralUser user) {
        this.user = user;
    }

    public Scheme getScheme() {
        return scheme;
    }

    public void setScheme(Scheme scheme) {
        this.scheme = scheme;
    }

    public LocalDateTime getApplicationDate() {
        return applicationDate;
    }

    public void setApplicationDate(LocalDateTime applicationDate) {
        this.applicationDate = applicationDate;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public byte[] getSupportingDocument() {
        return supportingDocument;
    }

    public void setSupportingDocument(byte[] supportingDocument) {
        this.supportingDocument = supportingDocument;
    }

    public String getDocumentContentType() {
        return documentContentType;
    }

    public void setDocumentContentType(String documentContentType) {
        this.documentContentType = documentContentType;
    }

    public List<ApplicationDocument> getApplicationDocuments() {
        return applicationDocuments;
    }

    public void setApplicationDocuments(List<ApplicationDocument> applicationDocuments) {
        this.applicationDocuments = applicationDocuments;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public byte[] getPaymentScreenshot() {
        return paymentScreenshot;
    }

    public void setPaymentScreenshot(byte[] paymentScreenshot) {
        this.paymentScreenshot = paymentScreenshot;
    }

    public String getPaymentScreenshotContentType() {
        return paymentScreenshotContentType;
    }

    public void setPaymentScreenshotContentType(String paymentScreenshotContentType) {
        this.paymentScreenshotContentType = paymentScreenshotContentType;
    }

    public Boolean getDocumentAccessGranted() {
        return documentAccessGranted;
    }

    public void setDocumentAccessGranted(Boolean documentAccessGranted) {
        this.documentAccessGranted = documentAccessGranted;
    }

    public LocalDateTime getDocumentAccessGrantedDate() {
        return documentAccessGrantedDate;
    }

    public void setDocumentAccessGrantedDate(LocalDateTime documentAccessGrantedDate) {
        this.documentAccessGrantedDate = documentAccessGrantedDate;
    }

    public Boolean getBondBroken() {
        return bondBroken;
    }

    public void setBondBroken(Boolean bondBroken) {
        this.bondBroken = bondBroken;
    }

    public LocalDateTime getBondBrokenDate() {
        return bondBrokenDate;
    }

    public void setBondBrokenDate(LocalDateTime bondBrokenDate) {
        this.bondBrokenDate = bondBrokenDate;
    }

    public String getBondBreakReason() {
        return bondBreakReason;
    }

    public void setBondBreakReason(String bondBreakReason) {
        this.bondBreakReason = bondBreakReason;
    }

    public Boolean getAdminVerified() {
        return adminVerified;
    }

    public void setAdminVerified(Boolean adminVerified) {
        this.adminVerified = adminVerified;
    }

    public LocalDateTime getAdminVerificationDate() {
        return adminVerificationDate;
    }

    public void setAdminVerificationDate(LocalDateTime adminVerificationDate) {
        this.adminVerificationDate = adminVerificationDate;
    }

    public String getAdminVerificationRemarks() {
        return adminVerificationRemarks;
    }

    public void setAdminVerificationRemarks(String adminVerificationRemarks) {
        this.adminVerificationRemarks = adminVerificationRemarks;
    }

    public Long getAdminVerifiedBy() {
        return adminVerifiedBy;
    }

    public void setAdminVerifiedBy(Long adminVerifiedBy) {
        this.adminVerifiedBy = adminVerifiedBy;
    }

    // Helper method to add a document
    public void addDocument(Document document, byte[] documentFile, String contentType, String fileName) {
        ApplicationDocument appDoc = new ApplicationDocument(this, document, documentFile, contentType, fileName);
        this.applicationDocuments.add(appDoc);
    }

    // Helper method to get a document by document type
    public ApplicationDocument getDocumentByType(Document document) {
        return this.applicationDocuments.stream()
                .filter(ad -> ad.getDocument().getId().equals(document.getId()))
                .findFirst()
                .orElse(null);
    }

    // Helper method to check if a document type has been uploaded
    public boolean hasDocument(Document document) {
        return this.applicationDocuments.stream()
                .anyMatch(ad -> ad.getDocument().getId().equals(document.getId()));
    }
}
