<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetCafe Approvals - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-user-shield"></i> Admin Panel
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    Welcome, <span th:text="${admin.username}"></span>
                </span>
                <a class="nav-link" href="/admin/logout">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="list-group">
                    <a href="/admin/dashboard" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="/admin/netcafe-approvals" class="list-group-item list-group-item-action active">
                        <i class="fas fa-user-check"></i> NetCafe Approvals
                    </a>
                    <a href="/admin/netcafe-users" class="list-group-item list-group-item-action">
                        <i class="fas fa-wifi"></i> NetCafe Users
                    </a>
                    <a href="/admin/users" class="list-group-item list-group-item-action">
                        <i class="fas fa-users"></i> General Users
                    </a>
                    <a href="/admin/schemes" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-alt"></i> Schemes
                    </a>
                    <a href="/admin/schemes/applications" class="list-group-item list-group-item-action">
                        <i class="fas fa-clipboard-list"></i> Applications
                    </a>
                    <a href="/admin/pending-payments" class="list-group-item list-group-item-action">
                        <i class="fas fa-credit-card"></i> Pending Payments
                    </a>
                    <a href="/admin/settlements" class="list-group-item list-group-item-action">
                        <i class="fas fa-money-bill-wave"></i> Settlements
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-user-check"></i> NetCafe Approvals</h2>
                </div>

                <!-- Flash Messages -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Pending Approvals -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-clock"></i> Pending NetCafe Approvals
                            <span class="badge bg-warning ms-2" th:text="${#lists.size(pendingApprovals)}"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(pendingApprovals)}" class="text-center py-4">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <h5>No Pending Approvals</h5>
                            <p class="text-muted">All NetCafe registrations have been processed.</p>
                        </div>

                        <div th:if="${!#lists.isEmpty(pendingApprovals)}" class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Phone</th>
                                        <th>NetCafe Name</th>
                                        <th>Address</th>
                                        <th>Registration Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="user : ${pendingApprovals}">
                                        <td th:text="${user.id}"></td>
                                        <td>
                                            <strong th:text="${user.name}"></strong>
                                        </td>
                                        <td th:text="${user.email}"></td>
                                        <td th:text="${user.phone}"></td>
                                        <td>
                                            <span class="badge bg-info" th:text="${user.netCafeName}"></span>
                                        </td>
                                        <td th:text="${user.address}"></td>
                                        <td th:text="${#temporals.format(user.registrationDate, 'dd-MM-yyyy HH:mm')}"></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a th:href="@{/admin/view-documents/{id}(id=${user.id})}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="View Documents">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <form th:action="@{/admin/approve/{id}(id=${user.id})}" 
                                                      method="post" 
                                                      style="display: inline;"
                                                      onsubmit="return confirm('Are you sure you want to approve this NetCafe user?')">
                                                    <button type="submit" 
                                                            class="btn btn-sm btn-success" 
                                                            title="Approve">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
