<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetCafe - Available Applications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #6f42c1;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-verified {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-not-verified {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/netcafe/available-applications}">
                            <i class="bi bi-file-earmark-plus"></i> Available Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/edit-profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>Available Applications</h2>
                    <p>Browse and claim scheme applications</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header">
                        <h5>Applications Available for Processing</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Scheme</th>
                                        <th>Applicant</th>
                                        <th>Application Date</th>
                                        <th>Admin Verification</th>
                                        <th>Payment Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="app : ${availableApplications}">
                                        <td th:text="${app.id}">1</td>
                                        <td th:text="${app.scheme != null ? app.scheme.name : 'Unknown'}">Scheme Name</td>
                                        <td th:text="${app.user != null ? app.user.name : 'Unknown'}">Applicant Name</td>
                                        <td th:text="${#temporals.format(app.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                        <td>
                                            <span th:if="${app.adminVerified != null && app.adminVerified}" class="badge badge-verified">
                                                <i class="bi bi-check-circle"></i> Verified
                                            </span>
                                            <span th:if="${app.adminVerified == null || !app.adminVerified}" class="badge badge-not-verified">
                                                <i class="bi bi-x-circle"></i> Not Verified
                                            </span>
                                        </td>
                                        <td>
                                            <span th:if="${app.scheme.paymentAmount == null}" class="badge bg-secondary">No Payment Required</span>
                                            <span th:if="${app.scheme.paymentAmount != null && app.paymentStatus == 'PENDING'}" class="badge bg-warning text-dark">Payment Pending</span>
                                            <span th:if="${app.scheme.paymentAmount != null && app.paymentStatus == 'VERIFIED'}" class="badge bg-success">Payment Verified</span>
                                            <span th:if="${app.scheme.paymentAmount != null && app.paymentStatus == 'COMPLETED'}" class="badge bg-primary">Payment Completed</span>
                                        </td>
                                        <td>
                                            <!-- Only show claim button if application is verified and payment is verified (if required) -->
                                            <div th:if="${app.adminVerified != null && app.adminVerified && (app.scheme.paymentAmount == null || app.paymentStatus == 'VERIFIED')}">
                                                <form th:action="@{/netcafe/claim-application/{id}(id=${app.id})}" method="post" class="d-inline">
                                                    <button type="submit" class="btn btn-sm btn-success">
                                                        <i class="bi bi-check-circle"></i> Claim
                                                    </button>
                                                </form>
                                            </div>
                                            <div th:if="${app.adminVerified == null || !app.adminVerified}">
                                                <button type="button" class="btn btn-sm btn-secondary" disabled title="Application must be verified by admin">
                                                    <i class="bi bi-lock"></i> Awaiting Verification
                                                </button>
                                            </div>
                                            <div th:if="${app.adminVerified != null && app.adminVerified && app.scheme.paymentAmount != null && app.paymentStatus != 'VERIFIED'}">
                                                <button type="button" class="btn btn-sm btn-warning" disabled title="Payment must be verified before claiming">
                                                    <i class="bi bi-credit-card"></i> Awaiting Payment
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr th:if="${availableApplications.empty}">
                                        <td colspan="7" class="text-center">No available applications found</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <a th:href="@{/netcafe/dashboard}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
