<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scheme Details</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #28a745;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .section-title {
            color: #28a745;
            margin-bottom: 15px;
            border-bottom: 2px solid #28a745;
            padding-bottom: 5px;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2 th:text="${scheme.name}">Scheme Name</h2>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <div class="mb-4">
                                    <h4 class="section-title">Description</h4>
                                    <p th:text="${scheme.description}">Scheme description...</p>
                                </div>

                                <div class="mb-4" th:if="${scheme.eligibility != null && !scheme.eligibility.isEmpty()}">
                                    <h4 class="section-title">Eligibility</h4>
                                    <p th:text="${scheme.eligibility}">Eligibility criteria...</p>
                                </div>

                                <div class="mb-4" th:if="${scheme.benefits != null && !scheme.benefits.isEmpty()}">
                                    <h4 class="section-title">Benefits</h4>
                                    <p th:text="${scheme.benefits}">Benefits...</p>
                                </div>

                                <div class="mb-4" th:if="${scheme.applicationProcess != null && !scheme.applicationProcess.isEmpty()}">
                                    <h4 class="section-title">Application Process</h4>
                                    <p th:text="${scheme.applicationProcess}">Application process...</p>
                                </div>

                                <div class="mb-4">
                                    <h4 class="section-title">Required Documents</h4>

                                    <!-- Display manually entered required documents if any -->
                                    <div th:if="${scheme.requiredDocuments != null && !scheme.requiredDocuments.isEmpty()}" class="mb-3">
                                        <h5 class="text-muted">General Requirements:</h5>
                                        <p th:text="${scheme.requiredDocuments}">Required documents...</p>
                                    </div>

                                    <!-- Display selected document types -->
                                    <div th:if="${!requiredDocuments.empty}" class="mb-3">
                                        <h5 class="text-muted">Specific Document Requirements:</h5>
                                        <div class="row">
                                            <div class="col-md-12">
                                                <ul class="list-group">
                                                    <li class="list-group-item" th:each="doc : ${requiredDocuments}">
                                                        <i class="bi bi-file-earmark-text text-success me-2"></i>
                                                        <strong th:text="${doc.name}">Document Name</strong>
                                                        <p class="text-muted small mb-0" th:if="${doc.description != null && !doc.description.isEmpty()}"
                                                           th:text="${doc.description}">Document description</p>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- No documents required message -->
                                    <div th:if="${(scheme.requiredDocuments == null || scheme.requiredDocuments.isEmpty()) && requiredDocuments.empty}" class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> No specific documents are required for this scheme.
                                    </div>
                                </div>

                                <div class="mb-4" th:if="${scheme.schemeDocument != null}">
                                    <h4 class="section-title">Scheme Document</h4>
                                    <a th:href="@{/admin/schemes/document/{id}(id=${scheme.id})}" class="btn btn-outline-success" target="_blank">
                                        <i class="bi bi-file-earmark-pdf"></i> Download Scheme Document
                                    </a>
                                </div>

                                <!-- Payment Information -->
                                <div class="mb-4" th:if="${scheme.paymentAmount != null}">
                                    <h4 class="section-title">Payment Information</h4>
                                    <div class="card bg-light">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h5 class="text-success">Required Payment</h5>
                                                    <p class="fs-4">₹<span th:text="${scheme.paymentAmount}">500.00</span></p>
                                                    <p th:if="${scheme.paymentDetails != null && !scheme.paymentDetails.isEmpty()}"
                                                       th:text="${scheme.paymentDetails}">Payment details...</p>
                                                    <p class="text-muted small">You will be asked to make this payment after submitting your application.</p>
                                                </div>
                                                <div class="col-md-6 text-center" th:if="${scheme.paymentQrCode != null}">
                                                    <h5>Payment QR Code</h5>
                                                    <img th:src="@{/admin/schemes/payment-qr/{id}(id=${scheme.id})}"
                                                         alt="Payment QR Code" class="img-thumbnail" style="max-width: 150px;">
                                                    <p class="text-muted small mt-2">Scan with any UPI app</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                Apply for this Scheme
                            </div>
                            <div class="card-body">
                                <div th:if="${hasApplied}" class="alert alert-info">
                                    <i class="bi bi-info-circle"></i> You have already applied for this scheme, but you can apply again if needed. Check your existing applications for status.
                                </div>
                                <form th:action="@{/user/apply-scheme/{id}(id=${scheme.id})}" method="post" enctype="multipart/form-data">
                                    <!-- Document upload section -->
                                    <div th:if="${!requiredDocuments.empty}" class="mb-4">
                                        <h5 class="mb-3">Upload Required Documents</h5>
                                        <p class="text-muted mb-3">Please upload each required document separately:</p>

                                        <div class="card mb-3" th:each="doc, docStat : ${requiredDocuments}">
                                            <div class="card-body">
                                                <h6 class="card-title" th:text="${doc.name}">Document Name</h6>
                                                <p class="card-text text-muted small" th:if="${doc.description}" th:text="${doc.description}">Document description</p>
                                                <div class="mb-2">
                                                    <div class="input-group">
                                                        <input type="file" class="form-control" th:id="'documentFile-' + ${docStat.index}"
                                                               th:name="'documentFiles'" required>
                                                        <button type="button" class="btn btn-outline-success fetch-document-btn"
                                                                th:data-document-id="${doc.id}"
                                                                th:data-input-id="'documentFile-' + ${docStat.index}">
                                                            <i class="bi bi-cloud-download"></i> Fetch My Document
                                                        </button>
                                                    </div>
                                                    <input type="hidden" th:name="'documentIds'" th:value="${doc.id}">
                                                    <input type="hidden" th:id="'preUploadedDocumentId-' + ${doc.id}" name="preUploadedDocumentIds" disabled>
                                                    <small class="text-muted">Click "Fetch My Document" to use a document you've already uploaded.</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Optional consolidated document upload (for backward compatibility) -->
                                    <div class="mb-3">
                                        <label for="document" class="form-label">Additional Supporting Document (Optional)</label>
                                        <input type="file" class="form-control" id="document" name="document">
                                        <small class="text-muted">You can upload an additional consolidated document if needed.</small>
                                    </div>

                                    <div class="alert alert-info" th:if="${requiredDocuments.empty}">
                                        <h5><i class="bi bi-info-circle"></i> No Specific Documents Required</h5>
                                        <p>This scheme doesn't require any specific documents. You can still upload a supporting document if you have one.</p>
                                    </div>

                                    <!-- Custom Fields Section -->
                                    <div th:if="${customFields != null and !customFields.empty}" class="mt-4 mb-4">
                                        <div class="card border-primary">
                                            <div class="card-header bg-primary text-white">
                                                <i class="bi bi-person-lines-fill me-2"></i> Additional Information Required
                                            </div>
                                            <div class="card-body">
                                                <div class="alert alert-info">
                                                    <i class="bi bi-info-circle"></i>
                                                    Please fill in the following information required for this scheme:
                                                </div>

                                                <div th:each="field : ${customFields}" class="mb-3">
                                                    <label th:for="'customField_' + ${field.fieldName}" class="form-label">
                                                        <span th:text="${field.fieldLabel}">Field Label</span>
                                                        <span th:if="${field.required}" class="text-danger">*</span>
                                                    </label>

                                                    <!-- Text Input -->
                                                    <input th:if="${field.fieldType == 'TEXT'}"
                                                           type="text"
                                                           class="form-control"
                                                           th:id="'customField_' + ${field.fieldName}"
                                                           th:name="'customField_' + ${field.fieldName}"
                                                           th:placeholder="${field.placeholder}"
                                                           th:required="${field.required}">

                                                    <!-- Email Input -->
                                                    <input th:if="${field.fieldType == 'EMAIL'}"
                                                           type="email"
                                                           class="form-control"
                                                           th:id="'customField_' + ${field.fieldName}"
                                                           th:name="'customField_' + ${field.fieldName}"
                                                           th:placeholder="${field.placeholder}"
                                                           th:required="${field.required}">

                                                    <!-- Phone Input -->
                                                    <input th:if="${field.fieldType == 'PHONE'}"
                                                           type="tel"
                                                           class="form-control"
                                                           th:id="'customField_' + ${field.fieldName}"
                                                           th:name="'customField_' + ${field.fieldName}"
                                                           th:placeholder="${field.placeholder}"
                                                           th:required="${field.required}">

                                                    <!-- Date Input -->
                                                    <input th:if="${field.fieldType == 'DATE'}"
                                                           type="date"
                                                           class="form-control"
                                                           th:id="'customField_' + ${field.fieldName}"
                                                           th:name="'customField_' + ${field.fieldName}"
                                                           th:required="${field.required}">

                                                    <!-- Number Input -->
                                                    <input th:if="${field.fieldType == 'NUMBER'}"
                                                           type="number"
                                                           class="form-control"
                                                           th:id="'customField_' + ${field.fieldName}"
                                                           th:name="'customField_' + ${field.fieldName}"
                                                           th:placeholder="${field.placeholder}"
                                                           th:required="${field.required}">

                                                    <!-- Textarea -->
                                                    <textarea th:if="${field.fieldType == 'TEXTAREA'}"
                                                              class="form-control"
                                                              th:id="'customField_' + ${field.fieldName}"
                                                              th:name="'customField_' + ${field.fieldName}"
                                                              th:placeholder="${field.placeholder}"
                                                              th:required="${field.required}"
                                                              rows="3"></textarea>

                                                    <!-- Select Dropdown -->
                                                    <select th:if="${field.fieldType == 'SELECT'}"
                                                            class="form-select"
                                                            th:id="'customField_' + ${field.fieldName}"
                                                            th:name="'customField_' + ${field.fieldName}"
                                                            th:required="${field.required}">
                                                        <option value="">Select an option</option>
                                                        <option th:each="option : ${#strings.listSplit(field.options, ',')}"
                                                                th:value="${#strings.trim(option)}"
                                                                th:text="${#strings.trim(option)}">Option</option>
                                                    </select>

                                                    <!-- Help Text -->
                                                    <div th:if="${field.helpText != null and !field.helpText.isEmpty()}"
                                                         class="form-text" th:text="${field.helpText}">Help text</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Information Fields -->
                                    <div th:if="${scheme.paymentAmount != null}" class="mt-4 mb-4">
                                        <div class="card border-info">
                                            <div class="card-header bg-info text-white">
                                                <i class="bi bi-credit-card me-2"></i> Payment Information
                                            </div>
                                            <div class="card-body">
                                                <div class="alert alert-warning">
                                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                                    <strong>Payment Required:</strong> This scheme requires a payment of
                                                    ₹<span th:text="${scheme.paymentAmount}">500.00</span>
                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="transactionId" class="form-label">Transaction ID / Reference Number</label>
                                                            <input type="text" class="form-control" id="transactionId" name="transactionId" required>
                                                            <small class="text-muted">Enter the transaction ID or reference number from your payment app</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="mb-3">
                                                            <label for="paymentScreenshot" class="form-label">Payment Screenshot</label>
                                                            <input type="file" class="form-control" id="paymentScreenshot" name="paymentScreenshot" accept="image/*" required>
                                                            <small class="text-muted">Upload a screenshot of your payment confirmation</small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="row mt-3" th:if="${scheme.paymentQrCode != null}">
                                                    <div class="col-md-6">
                                                        <h6>Payment Instructions:</h6>
                                                        <ol class="mb-0">
                                                            <li>Scan the QR code using any UPI app (Google Pay, PhonePe, Paytm, etc.)</li>
                                                            <li>Complete the payment for the amount shown</li>
                                                            <li>Take a screenshot of the payment confirmation</li>
                                                            <li>Enter the transaction ID and upload the screenshot</li>
                                                        </ol>
                                                        <p class="mt-2" th:if="${scheme.paymentDetails != null && !scheme.paymentDetails.isEmpty()}"
                                                           th:text="${scheme.paymentDetails}">Additional payment details...</p>
                                                    </div>
                                                    <div class="col-md-6 text-center">
                                                        <img th:src="@{/admin/schemes/payment-qr/{id}(id=${scheme.id})}"
                                                             alt="Payment QR Code" class="img-thumbnail" style="max-width: 150px;">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="d-grid gap-2 mt-3">
                                        <button type="submit" class="btn btn-success">Apply Now</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <a th:href="@{/user/schemes}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Schemes
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Document Fetching Modal -->
    <div class="modal fade" id="documentModal" tabindex="-1" aria-labelledby="documentModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-success text-white">
                    <h5 class="modal-title" id="documentModalLabel">Select Document</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i> Select a document from your library to use for this application.
                    </div>
                    <div id="documentList" class="row">
                        <div class="text-center py-5">
                            <div class="spinner-border text-success" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading your documents...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Document fetching functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Store the current document ID and input field ID
            let currentDocumentId = null;
            let currentInputId = null;

            // Get all fetch document buttons
            const fetchButtons = document.querySelectorAll('.fetch-document-btn');

            // Add click event to all fetch buttons
            fetchButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Store the document ID and input ID
                    currentDocumentId = this.getAttribute('data-document-id');
                    currentInputId = this.getAttribute('data-input-id');

                    // Show the modal
                    const modal = new bootstrap.Modal(document.getElementById('documentModal'));
                    modal.show();

                    // Fetch user documents
                    fetchUserDocuments();
                });
            });

            // Function to fetch user documents
            function fetchUserDocuments() {
                fetch('/api/user/documents')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to fetch documents');
                        }
                        return response.json();
                    })
                    .then(documents => {
                        displayDocuments(documents);
                    })
                    .catch(error => {
                        document.getElementById('documentList').innerHTML = `
                            <div class="col-12">
                                <div class="alert alert-danger">
                                    <i class="bi bi-exclamation-triangle"></i> ${error.message}
                                </div>
                            </div>
                        `;
                    });
            }

            // Function to display documents in the modal
            function displayDocuments(documents) {
                const documentList = document.getElementById('documentList');

                // Filter documents by the current document type if needed
                const filteredDocuments = documents.filter(doc => doc.documentId == currentDocumentId);

                if (filteredDocuments.length === 0) {
                    documentList.innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> You don't have any matching documents in your library.
                                <a href="/user/documents/upload?documentId=${currentDocumentId}" target="_blank" class="alert-link">
                                    Upload this document type first
                                </a>
                            </div>
                        </div>
                    `;
                    return;
                }

                // Create HTML for documents
                let html = '';
                filteredDocuments.forEach(doc => {
                    const verifiedBadge = doc.isVerified ?
                        '<span class="badge bg-success">Verified</span>' :
                        '<span class="badge bg-warning text-dark">Pending Verification</span>';

                    html += `
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">${doc.documentName}</h6>
                                    <p class="card-text small text-muted">File: ${doc.fileName}</p>
                                    <p class="card-text small">Uploaded: ${new Date(doc.uploadDate).toLocaleDateString()}</p>
                                    <p class="card-text">${verifiedBadge}</p>
                                    <button type="button" class="btn btn-sm btn-success select-document" data-document-id="${doc.id}">
                                        <i class="bi bi-check-circle"></i> Use This Document
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });

                documentList.innerHTML = html;

                // Add event listeners to select buttons
                document.querySelectorAll('.select-document').forEach(button => {
                    button.addEventListener('click', function() {
                        const docId = this.getAttribute('data-document-id');
                        selectDocument(docId);
                    });
                });
            }

            // Function to select a document
            function selectDocument(docId) {
                // Fetch the specific document
                fetch(`/api/user/documents/by-type/${currentDocumentId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Failed to fetch document details');
                        }
                        return response.json();
                    })
                    .then(doc => {
                        // Create a custom file object for the input
                        const inputField = document.getElementById(currentInputId);

                        // Set a custom attribute to indicate this is a pre-uploaded document
                        inputField.setAttribute('data-pre-uploaded', 'true');
                        inputField.setAttribute('data-document-id', doc.id);

                        // Disable the file input since we're using a pre-uploaded document
                        inputField.disabled = true;
                        inputField.required = false;

                        // Enable the hidden input for pre-uploaded document
                        const preUploadedInput = document.getElementById('preUploadedDocumentId-' + currentDocumentId);
                        if (preUploadedInput) {
                            preUploadedInput.value = currentDocumentId; // This should be the document type ID, not user document ID
                            preUploadedInput.disabled = false;
                        }

                        // Create a custom file list message
                        const fileInfo = document.createElement('div');
                        fileInfo.className = 'alert alert-success mt-2 mb-0';
                        fileInfo.innerHTML = `
                            <i class="bi bi-check-circle"></i> Using your uploaded document: <strong>${doc.fileName}</strong>
                            <button type="button" class="btn-close float-end" aria-label="Close"></button>
                        `;

                        // Replace any existing file info
                        const existingInfo = inputField.parentNode.querySelector('.alert');
                        if (existingInfo) {
                            existingInfo.remove();
                        }

                        // Add the file info after the input
                        inputField.parentNode.appendChild(fileInfo);

                        // Add event listener to the close button
                        fileInfo.querySelector('.btn-close').addEventListener('click', function() {
                            fileInfo.remove();
                            inputField.removeAttribute('data-pre-uploaded');
                            inputField.removeAttribute('data-document-id');
                            inputField.value = '';

                            // Re-enable the file input
                            inputField.disabled = false;
                            inputField.required = true;

                            // Disable the hidden input for pre-uploaded document
                            if (preUploadedInput) {
                                preUploadedInput.value = '';
                                preUploadedInput.disabled = true;
                            }
                        });

                        // Hide the modal
                        bootstrap.Modal.getInstance(document.getElementById('documentModal')).hide();
                    })
                    .catch(error => {
                        alert('Error: ' + error.message);
                    });
            }
        });
    </script>
</body>
</html>
