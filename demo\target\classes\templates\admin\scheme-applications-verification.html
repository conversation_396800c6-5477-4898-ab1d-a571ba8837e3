<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Scheme Applications Verification - Admin Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/admin/dashboard">
                <i class="fas fa-user-shield"></i> Admin Panel
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    Welcome, <span th:text="${admin.username}"></span>
                </span>
                <a class="nav-link" href="/admin/logout">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2">
                <div class="list-group">
                    <a href="/admin/dashboard" class="list-group-item list-group-item-action">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a href="/admin/netcafe-approvals" class="list-group-item list-group-item-action">
                        <i class="fas fa-user-check"></i> NetCafe Approvals
                    </a>
                    <a href="/admin/scheme-applications/pending-verification" class="list-group-item list-group-item-action active">
                        <i class="fas fa-clipboard-check"></i> Scheme Verification
                    </a>
                    <a href="/admin/netcafe-users" class="list-group-item list-group-item-action">
                        <i class="fas fa-wifi"></i> NetCafe Users
                    </a>
                    <a href="/admin/users" class="list-group-item list-group-item-action">
                        <i class="fas fa-users"></i> General Users
                    </a>
                    <a href="/admin/schemes" class="list-group-item list-group-item-action">
                        <i class="fas fa-file-alt"></i> Schemes
                    </a>
                    <a href="/admin/schemes/applications" class="list-group-item list-group-item-action">
                        <i class="fas fa-clipboard-list"></i> All Applications
                    </a>
                    <a href="/admin/pending-payments" class="list-group-item list-group-item-action">
                        <i class="fas fa-credit-card"></i> Pending Payments
                    </a>
                    <a href="/admin/settlements" class="list-group-item list-group-item-action">
                        <i class="fas fa-money-bill-wave"></i> Settlements
                    </a>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-clipboard-check"></i> Scheme Applications Verification</h2>
                </div>

                <!-- Flash Messages -->
                <div th:if="${success}" class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <span th:text="${success}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <div th:if="${error}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> <span th:text="${error}"></span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>

                <!-- Pending Verification Applications -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-hourglass-half"></i> Applications Pending Verification
                            <span class="badge bg-warning ms-2" th:text="${#lists.size(pendingApplications)}"></span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div th:if="${#lists.isEmpty(pendingApplications)}" class="text-center py-4">
                            <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                            <h5>No Applications Pending Verification</h5>
                            <p class="text-muted">All scheme applications have been verified.</p>
                        </div>

                        <div th:if="${!#lists.isEmpty(pendingApplications)}" class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>ID</th>
                                        <th>User</th>
                                        <th>Scheme</th>
                                        <th>Application Date</th>
                                        <th>Payment Status</th>
                                        <th>Payment Amount</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:each="application : ${pendingApplications}">
                                        <td th:text="${application.id}"></td>
                                        <td>
                                            <strong th:text="${application.user.name}"></strong><br>
                                            <small class="text-muted" th:text="${application.user.email}"></small>
                                        </td>
                                        <td>
                                            <span class="badge bg-info" th:text="${application.scheme.name}"></span>
                                        </td>
                                        <td th:text="${#temporals.format(application.applicationDate, 'dd-MM-yyyy HH:mm')}"></td>
                                        <td>
                                            <span th:if="${application.paymentStatus == 'PENDING'}" class="badge bg-warning">Pending</span>
                                            <span th:if="${application.paymentStatus == 'VERIFIED'}" class="badge bg-success">Verified</span>
                                            <span th:if="${application.paymentStatus == 'COMPLETED'}" class="badge bg-primary">Completed</span>
                                            <span th:if="${application.scheme.paymentAmount == null}" class="badge bg-secondary">No Payment Required</span>
                                        </td>
                                        <td>
                                            <span th:if="${application.scheme.paymentAmount != null}" 
                                                  th:text="'₹' + ${application.scheme.paymentAmount}"></span>
                                            <span th:if="${application.scheme.paymentAmount == null}" class="text-muted">Free</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-warning" th:text="${application.status}"></span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a th:href="@{/admin/schemes/applications/{id}(id=${application.id})}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button type="button" 
                                                        class="btn btn-sm btn-success" 
                                                        data-bs-toggle="modal" 
                                                        th:data-bs-target="'#verifyModal' + ${application.id}"
                                                        title="Verify">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" 
                                                        class="btn btn-sm btn-danger" 
                                                        data-bs-toggle="modal" 
                                                        th:data-bs-target="'#rejectModal' + ${application.id}"
                                                        title="Reject">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>

                                            <!-- Verify Modal -->
                                            <div class="modal fade" th:id="'verifyModal' + ${application.id}" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Verify Application</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <form th:action="@{/admin/scheme-applications/{id}/verify(id=${application.id})}" method="post">
                                                            <div class="modal-body">
                                                                <p>Are you sure you want to verify this application?</p>
                                                                <p><strong>User:</strong> <span th:text="${application.user.name}"></span></p>
                                                                <p><strong>Scheme:</strong> <span th:text="${application.scheme.name}"></span></p>
                                                                <div class="mb-3">
                                                                    <label for="remarks" class="form-label">Verification Remarks (Optional)</label>
                                                                    <textarea class="form-control" name="remarks" rows="3" placeholder="Enter any remarks..."></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                <button type="submit" class="btn btn-success">Verify Application</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Reject Modal -->
                                            <div class="modal fade" th:id="'rejectModal' + ${application.id}" tabindex="-1">
                                                <div class="modal-dialog">
                                                    <div class="modal-content">
                                                        <div class="modal-header">
                                                            <h5 class="modal-title">Reject Application</h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                        </div>
                                                        <form th:action="@{/admin/scheme-applications/{id}/reject-verification(id=${application.id})}" method="post">
                                                            <div class="modal-body">
                                                                <p>Are you sure you want to reject this application?</p>
                                                                <p><strong>User:</strong> <span th:text="${application.user.name}"></span></p>
                                                                <p><strong>Scheme:</strong> <span th:text="${application.scheme.name}"></span></p>
                                                                <div class="mb-3">
                                                                    <label for="rejectionReason" class="form-label">Rejection Reason <span class="text-danger">*</span></label>
                                                                    <textarea class="form-control" name="rejectionReason" rows="3" placeholder="Enter reason for rejection..." required></textarea>
                                                                </div>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                                <button type="submit" class="btn btn-danger">Reject Application</button>
                                                            </div>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
