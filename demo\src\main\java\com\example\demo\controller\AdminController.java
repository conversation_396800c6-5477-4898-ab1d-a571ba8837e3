package com.example.demo.controller;

import com.example.demo.model.Admin;
import com.example.demo.model.GeneralUser;
import com.example.demo.model.Message;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.PaymentRecord;
import com.example.demo.model.SchemeApplication;
import com.example.demo.model.SettlementRequest;
import com.example.demo.service.AdminService;
import com.example.demo.service.GeneralUserService;
import com.example.demo.service.MessageService;
import com.example.demo.service.NetCafeApplicationService;
import com.example.demo.service.NetCafeUserService;
import com.example.demo.service.PaymentService;
import com.example.demo.service.SchemeApplicationService;
import com.example.demo.service.SettlementService;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.util.List;

@Controller
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private AdminService adminService;

    @Autowired
    private NetCafeUserService netCafeUserService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @Autowired
    private GeneralUserService generalUserService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private SettlementService settlementService;

    @GetMapping("/login")
    public String showLoginForm() {
        return "admin/login";
    }

    @PostMapping("/login")
    public String login(
            @RequestParam String username,
            @RequestParam String password,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        if (adminService.authenticateAdmin(username, password)) {
            Admin admin = adminService.findByUsername(username).orElseThrow();
            session.setAttribute("admin", admin);
            return "redirect:/admin/dashboard";
        }

        redirectAttributes.addFlashAttribute("error", "Invalid username or password");
        return "redirect:/admin/login";
    }

    @GetMapping("/dashboard")
    public String dashboard(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        // NetCafe users
        List<NetCafeUser> pendingApprovals = netCafeUserService.findAllPendingApprovals();
        List<NetCafeUser> allNetCafeUsers = netCafeUserService.findAllUsers();
        List<NetCafeUser> approvedNetCafeUsers = netCafeUserService.findAllApprovedUsers();

        // General users
        long totalGeneralUsers = generalUserService.countAllUsers();
        long activeGeneralUsers = generalUserService.countActiveUsers();

        // Applications
        long totalApplications = schemeApplicationService.countAllApplications();
        long pendingApplications = schemeApplicationService.countApplicationsByStatus("PENDING");
        long approvedApplications = schemeApplicationService.countApplicationsByStatus("APPROVED");

        // NetCafe applications
        long totalNetCafeApplications = netCafeApplicationService.countAllApplications();
        long completedNetCafeApplications = netCafeApplicationService.countCompletedApplications();

        // Add all attributes to the model
        model.addAttribute("pendingApprovals", pendingApprovals);
        model.addAttribute("totalNetCafeUsers", allNetCafeUsers.size());
        model.addAttribute("approvedNetCafeUsers", approvedNetCafeUsers.size());

        model.addAttribute("totalGeneralUsers", totalGeneralUsers);
        model.addAttribute("activeGeneralUsers", activeGeneralUsers);

        model.addAttribute("totalApplications", totalApplications);
        model.addAttribute("pendingApplications", pendingApplications);
        model.addAttribute("approvedApplications", approvedApplications);

        model.addAttribute("totalNetCafeApplications", totalNetCafeApplications);
        model.addAttribute("completedNetCafeApplications", completedNetCafeApplications);

        model.addAttribute("admin", admin);

        return "admin/dashboard";
    }

    @PostMapping("/approve/{userId}")
    public String approveUser(
            @PathVariable Long userId,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            netCafeUserService.approveUser(userId);
            redirectAttributes.addFlashAttribute("success", "User approved successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error approving user: " + e.getMessage());
        }

        return "redirect:/admin/dashboard";
    }

    @GetMapping("/view-documents/{userId}")
    public String viewDocuments(
            @PathVariable Long userId,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            NetCafeUser user = netCafeUserService.findById(userId)
                    .orElseThrow(() -> new RuntimeException("User not found with ID: " + userId));

            model.addAttribute("user", user);
            model.addAttribute("admin", admin);

            return "admin/view-documents";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error retrieving user documents: " + e.getMessage());
            return "redirect:/admin/dashboard";
        }
    }

    @GetMapping("/pending-payments")
    public String viewPendingPayments(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<PaymentRecord> pendingPayments = paymentService.getAllPendingUserPayments();

        // Debug information
        System.out.println("DEBUG - Pending Payments Count: " + pendingPayments.size());

        // Get all payments for debugging
        List<PaymentRecord> allPayments = paymentService.getAllPayments();
        System.out.println("DEBUG - All Payments Count: " + allPayments.size());

        for (PaymentRecord payment : allPayments) {
            System.out.println("DEBUG - Payment ID: " + payment.getId() +
                               ", Type: " + payment.getPaymentType() +
                               ", Status: " + payment.getStatus() +
                               ", User: " + (payment.getUser() != null ? payment.getUser().getName() : "null") +
                               ", Transaction ID: " + payment.getTransactionId());
        }

        model.addAttribute("pendingPayments", pendingPayments);
        model.addAttribute("admin", admin);

        return "admin/pending-payments";
    }

    @GetMapping("/payment/{id}")
    public String viewPaymentDetails(@PathVariable Long id, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            PaymentRecord payment = paymentService.getPaymentById(id)
                    .orElseThrow(() -> new RuntimeException("Payment not found with ID: " + id));

            model.addAttribute("payment", payment);
            model.addAttribute("admin", admin);

            return "admin/payment-details";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error retrieving payment: " + e.getMessage());
            return "redirect:/admin/pending-payments";
        }
    }

    @PostMapping("/verify-payment/{id}")
    public String verifyPayment(
            @PathVariable Long id,
            @RequestParam(required = false) String remarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            PaymentRecord payment = paymentService.verifyUserPayment(id, remarks);

            // Update the application payment status
            if (payment.getApplication() != null) {
                SchemeApplication application = payment.getApplication();
                application.setPaymentStatus("VERIFIED");
                schemeApplicationService.saveApplication(application);
            }

            redirectAttributes.addFlashAttribute("success", "Payment verified successfully");
            return "redirect:/admin/pending-payments";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error verifying payment: " + e.getMessage());
            return "redirect:/admin/payment/" + id;
        }
    }

    @GetMapping("/application-payment-screenshot/{id}")
    public ResponseEntity<byte[]> getApplicationPaymentScreenshot(@PathVariable Long id, HttpSession session) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return new ResponseEntity<>(HttpStatus.UNAUTHORIZED);
        }

        Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

        if (applicationOpt.isPresent()) {
            SchemeApplication application = applicationOpt.get();
            byte[] screenshotData = application.getPaymentScreenshot();

            if (screenshotData != null) {
                HttpHeaders headers = new HttpHeaders();
                headers.setContentType(MediaType.parseMediaType(application.getPaymentScreenshotContentType()));
                return new ResponseEntity<>(screenshotData, headers, HttpStatus.OK);
            }
        }

        return new ResponseEntity<>(HttpStatus.NOT_FOUND);
    }

    @GetMapping("/create-test-payment")
    public String createTestPayment(HttpSession session, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Get a random scheme application
            List<SchemeApplication> applications = schemeApplicationService.getAllApplications();

            if (applications.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "No applications found to create test payment");
                return "redirect:/admin/pending-payments";
            }

            SchemeApplication application = applications.get(0);
            System.out.println("DEBUG - Creating test payment for application ID: " + application.getId());

            // Create a test payment
            PaymentRecord payment = new PaymentRecord();
            payment.setApplication(application);
            payment.setUser(application.getUser());
            payment.setPaymentType("USER_PAYMENT");

            // Set amount from scheme
            Double paymentAmountDouble = application.getScheme() != null && application.getScheme().getPaymentAmount() != null ?
                                        application.getScheme().getPaymentAmount() : 500.0;
            BigDecimal paymentAmount = BigDecimal.valueOf(paymentAmountDouble);
            payment.setAmount(paymentAmount);

            payment.setTransactionId("TEST-TXN-" + System.currentTimeMillis());
            payment.setPaymentDate(LocalDateTime.now());
            payment.setStatus("PENDING");
            payment.setPaymentMethod("UPI");
            payment.setRemarks("Test payment created by admin");
            payment.setIsSettled(false);

            PaymentRecord savedPayment = paymentService.savePayment(payment);

            System.out.println("DEBUG - Test payment created with ID: " + savedPayment.getId());

            redirectAttributes.addFlashAttribute("success", "Test payment created successfully with ID: " + savedPayment.getId());
            return "redirect:/admin/pending-payments";
        } catch (Exception e) {
            System.out.println("DEBUG - Error creating test payment: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error creating test payment: " + e.getMessage());
            return "redirect:/admin/pending-payments";
        }
    }

    // User Management Endpoints

    @GetMapping("/users")
    public String viewUsers(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<GeneralUser> users = generalUserService.getAllUsers();
        model.addAttribute("users", users);
        model.addAttribute("admin", admin);

        return "admin/users";
    }

    @GetMapping("/users/{id}")
    public String viewUserDetails(@PathVariable Long id, HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        Optional<GeneralUser> userOpt = generalUserService.getUserById(id);

        if (userOpt.isEmpty()) {
            return "redirect:/admin/users";
        }

        GeneralUser user = userOpt.get();
        List<SchemeApplication> applications = schemeApplicationService.getApplicationsByUser(user);

        model.addAttribute("user", user);
        model.addAttribute("applications", applications);
        model.addAttribute("admin", admin);

        return "admin/user-details";
    }

    @PostMapping("/users/{id}/toggle-status")
    public String toggleUserStatus(@PathVariable Long id, HttpSession session, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            GeneralUser user = generalUserService.toggleUserStatus(id);
            String status = user.isActive() ? "activated" : "deactivated";
            redirectAttributes.addFlashAttribute("success", "User " + user.getName() + " has been " + status);
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating user status: " + e.getMessage());
        }

        return "redirect:/admin/users";
    }

    @PostMapping("/users/{id}/delete")
    public String deleteUser(@PathVariable Long id, HttpSession session, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            generalUserService.deleteUser(id);
            redirectAttributes.addFlashAttribute("success", "User has been deleted");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error deleting user: " + e.getMessage());
        }

        return "redirect:/admin/users";
    }

    // NetCafe User Management Endpoints

    @GetMapping("/netcafe-users")
    public String viewNetCafeUsers(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<NetCafeUser> users = netCafeUserService.findAllUsers();
        model.addAttribute("users", users);
        model.addAttribute("admin", admin);

        return "admin/netcafe-users";
    }

    @GetMapping("/netcafe-users/{id}")
    public String viewNetCafeUserDetails(@PathVariable Long id, HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        Optional<NetCafeUser> userOpt = netCafeUserService.findById(id);

        if (userOpt.isEmpty()) {
            return "redirect:/admin/netcafe-users";
        }

        NetCafeUser user = userOpt.get();
        List<NetCafeApplication> applications = netCafeApplicationService.getApplicationsByNetCafeUser(user);

        model.addAttribute("user", user);
        model.addAttribute("applications", applications);
        model.addAttribute("admin", admin);

        return "admin/netcafe-user-details";
    }

    @PostMapping("/netcafe-users/{id}/toggle-status")
    public String toggleNetCafeUserStatus(@PathVariable Long id, HttpSession session, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            NetCafeUser user = netCafeUserService.toggleStatus(id);
            String status = user.isActive() ? "activated" : "deactivated";
            redirectAttributes.addFlashAttribute("success", "NetCafe user " + user.getName() + " has been " + status);
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating NetCafe user status: " + e.getMessage());
        }

        return "redirect:/admin/netcafe-users";
    }

    // Message Viewing Endpoints

    @GetMapping("/messages/{applicationId}")
    public String viewMessages(
            @PathVariable Long applicationId,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Find the NetCafe application
            List<NetCafeApplication> applications = netCafeApplicationService.getApplicationsBySchemeApplicationId(applicationId);

            if (applications.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "No NetCafe application found for this scheme application");
                return "redirect:/admin/schemes/applications/" + applicationId;
            }

            // Get the latest NetCafe application
            NetCafeApplication netCafeApplication = applications.get(0);

            // Get the scheme application
            SchemeApplication schemeApplication = netCafeApplication.getApplication();

            // Get all messages for this NetCafe application
            List<Message> messages = messageService.getMessagesByNetCafeApplication(netCafeApplication);

            // If there are no messages, create a test message
            if (messages.isEmpty()) {
                System.out.println("DEBUG - Admin View Messages - No messages found, creating test message");

                // Create a test message from USER
                messageService.sendMessage(netCafeApplication, "USER", "This is a test message from the user");

                // Create a test message from NETCAFE
                messageService.sendMessage(netCafeApplication, "NETCAFE", "This is a test response from NetCafe");

                // Refresh the messages list
                messages = messageService.getMessagesByNetCafeApplication(netCafeApplication);
            }

            // Debug logging
            System.out.println("DEBUG - Admin View Messages - Found " + messages.size() + " messages");
            for (Message message : messages) {
                System.out.println("DEBUG - Admin View Messages - Message ID: " + message.getId() +
                                  ", Sender: " + message.getSenderType() +
                                  ", Content: " + message.getContent() +
                                  ", Has Attachment: " + message.hasAttachment());
                if (message.hasAttachment()) {
                    System.out.println("DEBUG - Admin View Messages - Attachment Name: " + message.getAttachmentName() +
                                      ", Type: " + message.getAttachmentType() +
                                      ", Size: " + message.getAttachmentSize());
                }
            }

            model.addAttribute("admin", admin);
            model.addAttribute("netCafeApplication", netCafeApplication);
            model.addAttribute("application", schemeApplication);
            model.addAttribute("messages", messages);

            return "admin/messages";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error retrieving messages: " + e.getMessage());
            return "redirect:/admin/schemes/applications/" + applicationId;
        }
    }

    @GetMapping("/message-attachment/{id}")
    public ResponseEntity<byte[]> getMessageAttachment(@PathVariable Long id, HttpSession session) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        try {
            // Get the message
            Optional<Message> messageOpt = messageService.getMessageById(id);

            if (messageOpt.isEmpty() || !messageOpt.get().hasAttachment()) {
                return ResponseEntity.notFound().build();
            }

            Message message = messageOpt.get();

            // Return the attachment
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(message.getAttachmentContentType()));
            headers.setContentDispositionFormData("attachment", message.getAttachmentName());
            headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

            return new ResponseEntity<>(message.getAttachment(), headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    // Bond Management Endpoints

    @PostMapping("/applications/{id}/break-bond")
    public String breakBond(
            @PathVariable Long id,
            @RequestParam(required = false) String reason,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            // Find the NetCafe application
            List<NetCafeApplication> applications = netCafeApplicationService.getApplicationsBySchemeApplicationId(id);

            if (applications.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "No NetCafe application found for this scheme application");
                return "redirect:/admin/schemes/applications/" + id;
            }

            // Break the bond for the latest application
            NetCafeApplication latestApp = applications.get(0);
            latestApp.setBondBroken(true);
            latestApp.setBondBrokenDate(LocalDateTime.now());
            latestApp.setBondBreakReason(reason);
            latestApp.setBondBreakInitiator("ADMIN");

            netCafeApplicationService.saveApplication(latestApp);

            // Also update the scheme application
            SchemeApplication schemeApp = schemeApplicationService.getApplicationById(id).orElse(null);
            if (schemeApp != null) {
                schemeApp.setBondBroken(true);
                schemeApplicationService.saveApplication(schemeApp);
            }

            redirectAttributes.addFlashAttribute("success", "Bond has been broken successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error breaking bond: " + e.getMessage());
        }

        return "redirect:/admin/schemes/applications/" + id;
    }

    // Settlement Management Endpoints
    @GetMapping("/settlements")
    public String viewSettlements(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        // Get all settlement requests
        List<SettlementRequest> allRequests = settlementService.getAllSettlementRequests();
        List<SettlementRequest> pendingRequests = settlementService.getPendingSettlementRequests();

        model.addAttribute("admin", admin);
        model.addAttribute("allRequests", allRequests);
        model.addAttribute("pendingRequests", pendingRequests);

        return "admin/settlements";
    }

    @GetMapping("/settlements/{id}")
    public String viewSettlementDetails(@PathVariable Long id, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            SettlementRequest request = settlementService.getSettlementRequestById(id)
                    .orElseThrow(() -> new RuntimeException("Settlement request not found"));

            // Calculate current available balance for the NetCafe user
            BigDecimal currentBalance = settlementService.calculateAvailableBalance(request.getNetCafeUser());

            model.addAttribute("admin", admin);
            model.addAttribute("request", request);
            model.addAttribute("currentBalance", currentBalance);

            return "admin/settlement-details";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading settlement request: " + e.getMessage());
            return "redirect:/admin/settlements";
        }
    }

    @PostMapping("/settlements/{id}/approve")
    public String approveSettlement(
            @PathVariable Long id,
            @RequestParam(required = false) String adminRemarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            settlementService.approveSettlementRequest(id, adminRemarks);
            redirectAttributes.addFlashAttribute("success", "Settlement request approved successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error approving settlement request: " + e.getMessage());
        }

        return "redirect:/admin/settlements/" + id;
    }

    @PostMapping("/settlements/{id}/reject")
    public String rejectSettlement(
            @PathVariable Long id,
            @RequestParam String rejectionReason,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            settlementService.rejectSettlementRequest(id, rejectionReason);
            redirectAttributes.addFlashAttribute("success", "Settlement request rejected");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error rejecting settlement request: " + e.getMessage());
        }

        return "redirect:/admin/settlements/" + id;
    }

    @PostMapping("/settlements/{id}/complete")
    public String completeSettlement(
            @PathVariable Long id,
            @RequestParam String transactionId,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            settlementService.completeSettlementRequest(id, transactionId);
            redirectAttributes.addFlashAttribute("success", "Settlement completed successfully. NetCafe commissions have been settled.");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error completing settlement: " + e.getMessage());
        }

        return "redirect:/admin/settlements/" + id;
    }

    @GetMapping("/netcafe-approvals")
    public String viewNetCafeApprovals(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        List<NetCafeUser> pendingApprovals = netCafeUserService.findAllPendingApprovals();
        model.addAttribute("pendingApprovals", pendingApprovals);
        model.addAttribute("admin", admin);

        return "admin/netcafe-approvals";
    }

    // Scheme Application Verification Endpoints

    @GetMapping("/scheme-applications/pending-verification")
    public String viewPendingSchemeApplications(HttpSession session, Model model) {
        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        // Get applications that are pending admin verification
        List<SchemeApplication> pendingApplications = schemeApplicationService.getAllApplications()
                .stream()
                .filter(app -> app.getAdminVerified() == null || !app.getAdminVerified())
                .filter(app -> "PENDING".equals(app.getStatus()))
                .toList();

        model.addAttribute("pendingApplications", pendingApplications);
        model.addAttribute("admin", admin);

        return "admin/scheme-applications-verification";
    }

    @PostMapping("/scheme-applications/{id}/verify")
    public String verifySchemeApplication(
            @PathVariable Long id,
            @RequestParam(required = false) String remarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/scheme-applications/pending-verification";
            }

            SchemeApplication application = applicationOpt.get();
            application.setAdminVerified(true);
            application.setAdminVerificationDate(LocalDateTime.now());
            application.setAdminVerificationRemarks(remarks);
            application.setAdminVerifiedBy(admin.getId());

            schemeApplicationService.saveApplication(application);

            redirectAttributes.addFlashAttribute("success", "Application verified successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error verifying application: " + e.getMessage());
        }

        return "redirect:/admin/scheme-applications/pending-verification";
    }

    @PostMapping("/scheme-applications/{id}/reject-verification")
    public String rejectSchemeApplicationVerification(
            @PathVariable Long id,
            @RequestParam String rejectionReason,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/scheme-applications/pending-verification";
            }

            SchemeApplication application = applicationOpt.get();
            application.setStatus("REJECTED");
            application.setRemarks(rejectionReason);
            application.setAdminVerified(false);
            application.setAdminVerificationDate(LocalDateTime.now());
            application.setAdminVerificationRemarks(rejectionReason);
            application.setAdminVerifiedBy(admin.getId());

            schemeApplicationService.saveApplication(application);

            redirectAttributes.addFlashAttribute("success", "Application rejected successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error rejecting application: " + e.getMessage());
        }

        return "redirect:/admin/scheme-applications/pending-verification";
    }

    @PostMapping("/scheme-applications/{id}/approve-complete")
    public String approveCompleteApplication(
            @PathVariable Long id,
            @RequestParam(required = false) String remarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        Admin admin = (Admin) session.getAttribute("admin");

        if (admin == null) {
            return "redirect:/admin/login";
        }

        try {
            Optional<SchemeApplication> applicationOpt = schemeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/admin/scheme-applications/pending-verification";
            }

            SchemeApplication application = applicationOpt.get();

            // Complete approval: verify application AND payment (if required)
            application.setAdminVerified(true);
            application.setAdminVerificationDate(LocalDateTime.now());
            application.setAdminVerificationRemarks(remarks);
            application.setAdminVerifiedBy(admin.getId());

            // If payment is required, also verify the payment
            if (application.getScheme().getPaymentAmount() != null) {
                application.setPaymentStatus("VERIFIED");
            }

            schemeApplicationService.saveApplication(application);

            String message = "Application approved successfully";
            if (application.getScheme().getPaymentAmount() != null) {
                message += " and payment verified";
            }
            message += ". It is now available for NetCafe claiming.";

            redirectAttributes.addFlashAttribute("success", message);
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error approving application: " + e.getMessage());
        }

        return "redirect:/admin/scheme-applications/pending-verification";
    }



    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.removeAttribute("admin");
        return "redirect:/";
    }
}
