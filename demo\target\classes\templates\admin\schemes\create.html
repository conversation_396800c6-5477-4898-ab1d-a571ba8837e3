<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Scheme</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 250px;
            padding: 20px;
        }
        .header-card {
            background-color: #dc3545;
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .card-header {
            background-color: #f8f9fa;
            font-weight: bold;
            border-bottom: none;
        }
        .btn-danger {
            background-color: #dc3545;
            border: none;
        }
        .btn-danger:hover {
            background-color: #c82333;
        }
        @media (max-width: 768px) {
            .sidebar {
                width: 100%;
                height: auto;
                position: relative;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>Admin Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/schemes/applications}">
                            <i class="bi bi-file-earmark-text"></i> Scheme Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/admin/schemes}">
                            <i class="bi bi-list-check"></i> Manage Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/documents}">
                            <i class="bi bi-file-earmark"></i> Document Types
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/admin/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>Create New Scheme</h2>
                    <p>Add a new government scheme to the system</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-body">
                        <form id="schemeForm" th:action="@{/admin/schemes/create}" method="post" enctype="multipart/form-data" th:object="${scheme}">
                            <div class="mb-3">
                                <label for="name" class="form-label">Scheme Name</label>
                                <input type="text" class="form-control" id="name" th:field="*{name}" required>
                            </div>

                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" th:field="*{description}" rows="4" required></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="eligibility" class="form-label">Eligibility Criteria</label>
                                <textarea class="form-control" id="eligibility" th:field="*{eligibility}" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="benefits" class="form-label">Benefits</label>
                                <textarea class="form-control" id="benefits" th:field="*{benefits}" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="applicationProcess" class="form-label">Application Process</label>
                                <textarea class="form-control" id="applicationProcess" th:field="*{applicationProcess}" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="requiredDocuments" class="form-label">Required Documents (Manual Entry)</label>
                                <textarea class="form-control" id="requiredDocuments" th:field="*{requiredDocuments}" rows="3"></textarea>
                                <small class="text-muted">You can manually enter required documents here, or select from the document types below.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Required Document Types</label>
                                <div class="accordion" id="documentAccordion">
                                    <div th:each="category : ${categories}" class="accordion-item">
                                        <h2 class="accordion-header" th:id="'heading-' + ${category.replace(' ', '-')}">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse"
                                                    th:data-bs-target="'#collapse-' + ${category.replace(' ', '-')}" aria-expanded="false"
                                                    th:aria-controls="'collapse-' + ${category.replace(' ', '-')}">
                                                <span th:text="${category}">Category Name</span>
                                            </button>
                                        </h2>
                                        <div th:id="'collapse-' + ${category.replace(' ', '-')}" class="accordion-collapse collapse"
                                             th:aria-labelledby="'heading-' + ${category.replace(' ', '-')}" data-bs-parent="#documentAccordion">
                                            <div class="accordion-body">
                                                <div class="row">
                                                    <div th:each="doc : ${documents}" class="col-md-6 mb-2" th:if="${doc.category == category}">
                                                        <div class="form-check">
                                                            <input class="form-check-input" type="checkbox" name="documentIds" th:value="${doc.id}"
                                                                   th:id="'doc-' + ${doc.id}" th:title="${doc.name}" title="Select document: [[${doc.name}]]">
                                                            <label class="form-check-label" th:for="'doc-' + ${doc.id}" th:text="${doc.name}">
                                                                Document Name
                                                            </label>
                                                            <small class="text-muted d-block" th:if="${doc.description}" th:text="${doc.description}">
                                                                Document description
                                                            </small>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="documentFile" class="form-label">Scheme Document</label>
                                <input type="file" class="form-control" id="documentFile" name="documentFile">
                                <small class="text-muted">Upload a PDF document with detailed information about the scheme.</small>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="active" th:field="*{active}" checked>
                                <label class="form-check-label" for="active">Active</label>
                                <small class="text-muted d-block">If checked, the scheme will be visible to users.</small>
                            </div>

                            <!-- Custom Application Fields -->
                            <div class="card mb-3">
                                <div class="card-header bg-success text-white">
                                    <i class="bi bi-list-ul me-2"></i> Custom Application Fields
                                </div>
                                <div class="card-body">
                                    <p class="text-muted">Define custom fields that users will need to fill when applying for this scheme.</p>

                                    <div id="customFields">
                                        <!-- Default fields will be added here -->
                                    </div>

                                    <button type="button" class="btn btn-outline-success btn-sm" onclick="addCustomField()">
                                        <i class="bi bi-plus-circle"></i> Add Custom Field
                                    </button>

                                    <div class="form-check mt-3">
                                        <input type="checkbox" class="form-check-input" id="createDefaultFields" name="createDefaultFields" checked>
                                        <label class="form-check-label" for="createDefaultFields">
                                            Create default fields (Name, Email, Phone, Date of Birth, Address)
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Settings -->
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <i class="bi bi-credit-card me-2"></i> Payment Settings
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="paymentAmount" class="form-label">Payment Amount (₹)</label>
                                        <input type="number" class="form-control" id="paymentAmount" name="paymentAmount"
                                               step="0.01" min="0">
                                        <small class="text-muted">Enter the amount to be paid by applicants (leave empty if no payment is required)</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="upiId" class="form-label">UPI ID</label>
                                        <input type="text" class="form-control" id="upiId" name="upiId" placeholder="example@upi">
                                        <small class="text-muted">Enter your UPI ID to generate a payment QR code</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentDetails" class="form-label">Payment Instructions</label>
                                        <textarea class="form-control" id="paymentDetails" name="paymentDetails" rows="3"></textarea>
                                        <small class="text-muted">Additional payment instructions or details for applicants</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Commission Settings -->
                            <div class="card mb-3">
                                <div class="card-header bg-warning text-dark">
                                    <i class="bi bi-cash-coin me-2"></i> NetCafe Commission Settings
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>Commission Configuration</strong><br>
                                        Set the commission structure that NetCafe users will earn for processing applications for this scheme.
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Commission Type</label>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="commissionType" id="fixedCommission" value="fixed" checked>
                                            <label class="form-check-label" for="fixedCommission">
                                                Fixed Amount
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="commissionType" id="percentageCommission" value="percentage">
                                            <label class="form-check-label" for="percentageCommission">
                                                Percentage of Payment Amount
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3" id="fixedCommissionDiv">
                                        <label for="commissionAmount" class="form-label">Commission Amount</label>
                                        <div class="input-group">
                                            <span class="input-group-text">₹</span>
                                            <input type="number" class="form-control" id="commissionAmount" name="commissionAmount"
                                                   step="0.01" min="0" value="50" placeholder="50.00">
                                        </div>
                                        <div class="form-text">Fixed amount that NetCafe will receive for each application processed.</div>
                                    </div>

                                    <div class="mb-3 d-none" id="percentageCommissionDiv">
                                        <label for="commissionPercentage" class="form-label">Commission Percentage</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="commissionPercentage" name="commissionPercentage"
                                                   step="0.01" min="0" max="100" value="10" placeholder="10.00">
                                            <span class="input-group-text">%</span>
                                        </div>
                                        <div class="form-text">Percentage of the scheme payment amount that NetCafe will receive.</div>
                                    </div>

                                    <div class="alert alert-secondary">
                                        <small>
                                            <strong>Note:</strong> Commission will be paid to NetCafe users when they successfully complete application processing.
                                            You can modify these settings later from the Commissions section.
                                        </small>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <a th:href="@{/admin/schemes}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-danger">Create Scheme</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let fieldCounter = 0;

        // Commission type switching
        document.addEventListener('DOMContentLoaded', function() {
            const fixedRadio = document.getElementById('fixedCommission');
            const percentageRadio = document.getElementById('percentageCommission');
            const fixedDiv = document.getElementById('fixedCommissionDiv');
            const percentageDiv = document.getElementById('percentageCommissionDiv');

            fixedRadio.addEventListener('change', function() {
                if (this.checked) {
                    fixedDiv.classList.remove('d-none');
                    percentageDiv.classList.add('d-none');
                    document.getElementById('commissionPercentage').value = '';
                }
            });

            percentageRadio.addEventListener('change', function() {
                if (this.checked) {
                    fixedDiv.classList.add('d-none');
                    percentageDiv.classList.remove('d-none');
                    document.getElementById('commissionAmount').value = '';
                }
            });
        });

        function validateFieldName(input) {
            const fieldName = input.value.trim().toLowerCase();
            const currentFieldDiv = input.closest('.custom-field-item');
            const allFieldNameInputs = document.querySelectorAll('input[name*=".fieldName"]');

            // Clear previous validation
            input.classList.remove('is-invalid');
            let existingFeedback = input.parentNode.querySelector('.invalid-feedback');
            if (existingFeedback) {
                existingFeedback.remove();
            }

            if (fieldName === '') {
                return;
            }

            // Check for duplicates
            let duplicateFound = false;
            allFieldNameInputs.forEach(otherInput => {
                if (otherInput !== input &&
                    otherInput.closest('.custom-field-item') !== currentFieldDiv &&
                    otherInput.value.trim().toLowerCase() === fieldName) {
                    duplicateFound = true;
                }
            });

            if (duplicateFound) {
                input.classList.add('is-invalid');
                const feedback = document.createElement('div');
                feedback.className = 'invalid-feedback';
                feedback.textContent = `Field name "${input.value.trim()}" already exists. Please use a different name.`;
                input.parentNode.appendChild(feedback);
            }
        }

        function addCustomField() {
            fieldCounter++;
            const fieldsContainer = document.getElementById('customFields');

            const fieldHtml = `
                <div class="card mb-3 custom-field" id="field-${fieldCounter}">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <span>Custom Field #${fieldCounter}</span>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeField(${fieldCounter})">
                            <i class="bi bi-trash"></i> Remove
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Field Name</label>
                                    <input type="text" class="form-control" name="customFields[${fieldCounter}].fieldName"
                                           placeholder="e.g., father_name" required onblur="validateFieldName(this)" oninput="validateFieldName(this)">
                                    <small class="text-muted">Internal name (no spaces, use underscore)</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Field Label</label>
                                    <input type="text" class="form-control" name="customFields[${fieldCounter}].fieldLabel"
                                           placeholder="e.g., Father's Name" required>
                                    <small class="text-muted">Label shown to users</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Field Type</label>
                                    <select class="form-control" name="customFields[${fieldCounter}].fieldType" required>
                                        <option value="TEXT">Text</option>
                                        <option value="EMAIL">Email</option>
                                        <option value="PHONE">Phone</option>
                                        <option value="DATE">Date</option>
                                        <option value="NUMBER">Number</option>
                                        <option value="TEXTAREA">Textarea</option>
                                        <option value="SELECT">Dropdown</option>
                                        <option value="CHECKBOX">Checkbox</option>
                                        <option value="RADIO">Radio Button</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Placeholder</label>
                                    <input type="text" class="form-control" name="customFields[${fieldCounter}].placeholder"
                                           placeholder="Enter placeholder text">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Display Order</label>
                                    <input type="number" class="form-control" name="customFields[${fieldCounter}].displayOrder"
                                           value="${fieldCounter * 10}" min="1">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Help Text</label>
                                    <input type="text" class="form-control" name="customFields[${fieldCounter}].helpText"
                                           placeholder="Additional help text">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Options (for Select/Radio/Checkbox)</label>
                                    <input type="text" class="form-control" name="customFields[${fieldCounter}].options"
                                           placeholder="Option1,Option2,Option3">
                                    <small class="text-muted">Comma-separated values</small>
                                </div>
                            </div>
                        </div>
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="customFields[${fieldCounter}].required" value="true">
                            <label class="form-check-label">Required Field</label>
                        </div>
                    </div>
                </div>
            `;

            fieldsContainer.insertAdjacentHTML('beforeend', fieldHtml);
        }

        function removeField(fieldId) {
            const fieldElement = document.getElementById(`field-${fieldId}`);
            if (fieldElement) {
                fieldElement.remove();
            }
        }

        function validateFormBeforeSubmit() {
            const fieldNameInputs = document.querySelectorAll('input[name*=".fieldName"]');
            const fieldNames = new Set();
            let hasError = false;

            // Clear all previous validation
            fieldNameInputs.forEach(input => {
                input.classList.remove('is-invalid');
                let existingFeedback = input.parentNode.querySelector('.invalid-feedback');
                if (existingFeedback) {
                    existingFeedback.remove();
                }
            });

            // Check for duplicates
            fieldNameInputs.forEach(input => {
                const fieldName = input.value.trim().toLowerCase();
                if (fieldName !== '') {
                    if (fieldNames.has(fieldName)) {
                        input.classList.add('is-invalid');
                        const feedback = document.createElement('div');
                        feedback.className = 'invalid-feedback';
                        feedback.textContent = `Field name "${input.value.trim()}" is duplicated. Please use unique field names.`;
                        input.parentNode.appendChild(feedback);
                        hasError = true;
                    } else {
                        fieldNames.add(fieldName);
                    }
                }
            });

            if (hasError) {
                alert('Please fix the duplicate field names before submitting.');
                return false;
            }

            return true;
        }

        // Add one default custom field on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Add form validation on submit
            const form = document.getElementById('schemeForm');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!validateFormBeforeSubmit()) {
                        e.preventDefault();
                        return false;
                    }
                });
            }

            // You can uncomment this if you want a default field
            // addCustomField();
        });
    </script>
</body>
</html>
