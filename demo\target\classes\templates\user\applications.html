<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Applications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #28a745;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-approved {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-rejected {
            background-color: #dc3545;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-verified {
            background-color: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-not-verified {
            background-color: #6c757d;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .table th {
            background-color: #f8f9fa;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>User Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/schemes}">
                            <i class="bi bi-list-check"></i> Available Schemes
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" href="#">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/user/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>My Applications</h2>
                    <p>View and track all your scheme applications</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header">
                        Application History
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Application ID</th>
                                        <th>Scheme Name</th>
                                        <th>Date Applied</th>
                                        <th>Status</th>
                                        <th>Admin Verification</th>
                                        <th>Payment Status</th>
                                        <th>Remarks</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr th:if="${applications.empty}">
                                        <td colspan="8" class="text-center">No applications found</td>
                                    </tr>
                                    <tr th:each="app : ${applications}">
                                        <td th:text="${app.id}">1</td>
                                        <td th:text="${app.scheme.name}">Scheme Name</td>
                                        <td th:text="${#temporals.format(app.applicationDate, 'dd-MM-yyyy HH:mm')}">01-01-2023</td>
                                        <td>
                                            <span th:if="${app.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                            <span th:if="${app.status == 'APPROVED'}" class="badge badge-approved">Approved</span>
                                            <span th:if="${app.status == 'REJECTED'}" class="badge badge-rejected">Rejected</span>
                                        </td>
                                        <td>
                                            <span th:if="${app.adminVerified != null && app.adminVerified}" class="badge badge-verified">
                                                <i class="bi bi-check-circle"></i> Verified
                                            </span>
                                            <span th:if="${app.adminVerified == null || !app.adminVerified}" class="badge badge-not-verified">
                                                <i class="bi bi-clock"></i> Pending Verification
                                            </span>
                                        </td>
                                        <td>
                                            <span th:if="${app.scheme.paymentAmount == null}" class="badge bg-secondary">No Payment Required</span>
                                            <span th:if="${app.scheme.paymentAmount != null && app.paymentStatus == 'PENDING'}" class="badge bg-warning text-dark">Payment Pending</span>
                                            <span th:if="${app.scheme.paymentAmount != null && app.paymentStatus == 'VERIFIED'}" class="badge bg-success">Payment Verified</span>
                                            <span th:if="${app.scheme.paymentAmount != null && app.paymentStatus == 'COMPLETED'}" class="badge bg-primary">Payment Completed</span>
                                        </td>
                                        <td th:text="${app.remarks != null ? app.remarks : '-'}">Remarks</td>
                                        <td>
                                            <a th:href="@{/user/application/{id}(id=${app.id})}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-12">
                        <a th:href="@{/user/schemes}" class="btn btn-success">
                            <i class="bi bi-plus-circle"></i> Apply for New Scheme
                        </a>
                        <a th:href="@{/user/dashboard}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
