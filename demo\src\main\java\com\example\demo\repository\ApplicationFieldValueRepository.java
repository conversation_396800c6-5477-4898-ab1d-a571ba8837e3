package com.example.demo.repository;

import com.example.demo.model.ApplicationFieldValue;
import com.example.demo.model.SchemeApplication;
import com.example.demo.model.SchemeField;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ApplicationFieldValueRepository extends JpaRepository<ApplicationFieldValue, Long> {
    
    List<ApplicationFieldValue> findByApplication(SchemeApplication application);
    
    List<ApplicationFieldValue> findByApplicationId(Long applicationId);
    
    Optional<ApplicationFieldValue> findByApplicationAndSchemeField(SchemeApplication application, SchemeField schemeField);
    
    @Query("SELECT afv FROM ApplicationFieldValue afv WHERE afv.application.id = :applicationId ORDER BY afv.schemeField.displayOrder ASC")
    List<ApplicationFieldValue> findByApplicationIdOrderByFieldOrder(@Param("applicationId") Long applicationId);
    
    void deleteByApplication(SchemeApplication application);
    
    void deleteBySchemeField(SchemeField schemeField);
}
