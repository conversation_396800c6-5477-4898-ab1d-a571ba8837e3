<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NetCafe - Applications</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .sidebar {
            background-color: #212529;
            color: white;
            height: 100vh;
            position: fixed;
            padding-top: 20px;
        }
        .sidebar-link {
            color: #adb5bd;
            text-decoration: none;
            display: block;
            padding: 10px 15px;
            transition: all 0.3s;
        }
        .sidebar-link:hover, .sidebar-link.active {
            color: white;
            background-color: #343a40;
        }
        .sidebar-link i {
            margin-right: 10px;
        }
        .content {
            margin-left: 16.66%;
            padding: 20px;
        }
        .header-card {
            background-color: #6f42c1;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .badge-pending {
            background-color: #ffc107;
            color: #212529;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-processing {
            background-color: #17a2b8;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        .badge-completed {
            background-color: #28a745;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
        }
        @media (max-width: 768px) {
            .sidebar {
                position: static;
                height: auto;
            }
            .content {
                margin-left: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="text-center mb-4">
                    <h4>NetCafe Portal</h4>
                </div>
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/dashboard}">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link active" th:href="@{/netcafe/applications}">
                            <i class="bi bi-file-earmark-text"></i> My Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/available-applications}">
                            <i class="bi bi-file-earmark-plus"></i> Available Applications
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/payments}">
                            <i class="bi bi-credit-card"></i> Payment History
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/settlement}">
                            <i class="bi bi-bank"></i> Settlement
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/edit-profile}">
                            <i class="bi bi-person"></i> Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="sidebar-link" th:href="@{/netcafe/logout}">
                            <i class="bi bi-box-arrow-right"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 content">
                <div class="header-card">
                    <h2>My Applications</h2>
                    <p>Manage and process your claimed applications</p>
                </div>

                <div class="alert alert-success" role="alert" th:if="${success}" th:text="${success}"></div>
                <div class="alert alert-danger" role="alert" th:if="${error}" th:text="${error}"></div>

                <div class="card">
                    <div class="card-header">
                        <ul class="nav nav-tabs card-header-tabs">
                            <li class="nav-item">
                                <a class="nav-link active" id="all-tab" data-bs-toggle="tab" href="#all">All Applications</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pending-tab" data-bs-toggle="tab" href="#pending">Pending</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="processing-tab" data-bs-toggle="tab" href="#processing">Processing</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="completed-tab" data-bs-toggle="tab" href="#completed">Completed</a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <div class="tab-content">
                            <div class="tab-pane fade show active" id="all">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Scheme</th>
                                                <th>Applicant</th>
                                                <th>Application Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:each="app : ${applications}">
                                                <td th:text="${app.application.id}">1</td>
                                                <td th:text="${app.application.scheme != null ? app.application.scheme.name : 'Unknown'}">Scheme Name</td>
                                                <td th:text="${app.application.user != null ? app.application.user.name : 'Unknown'}">Applicant Name</td>
                                                <td th:text="${#temporals.format(app.application.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                                <td>
                                                    <span th:if="${app.status == 'PENDING'}" class="badge badge-pending">Pending</span>
                                                    <span th:if="${app.status == 'PROCESSING'}" class="badge badge-processing">Processing</span>
                                                    <span th:if="${app.status == 'COMPLETED'}" class="badge badge-completed">Completed</span>
                                                </td>
                                                <td>
                                                    <a th:href="@{/netcafe/application/{id}(id=${app.id})}" class="btn btn-sm btn-primary">
                                                        <i class="bi bi-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr th:if="${applications.empty}">
                                                <td colspan="6" class="text-center">No applications found</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="pending">
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Scheme</th>
                                                <th>Applicant</th>
                                                <th>Application Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr th:each="app : ${applications}" th:if="${app.status == 'PENDING'}">
                                                <td th:text="${app.application.id}">1</td>
                                                <td th:text="${app.application.scheme != null ? app.application.scheme.name : 'Unknown'}">Scheme Name</td>
                                                <td th:text="${app.application.user != null ? app.application.user.name : 'Unknown'}">Applicant Name</td>
                                                <td th:text="${#temporals.format(app.application.applicationDate, 'dd-MM-yyyy')}">01-01-2023</td>
                                                <td>
                                                    <a th:href="@{/netcafe/application/{id}(id=${app.id})}" class="btn btn-sm btn-primary">
                                                        <i class="bi bi-eye"></i> View
                                                    </a>
                                                </td>
                                            </tr>
                                            <tr th:if="${applications.?[status == 'PENDING'].empty}">
                                                <td colspan="5" class="text-center">No pending applications found</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="processing">
                                <!-- Similar table for processing applications -->
                            </div>
                            <div class="tab-pane fade" id="completed">
                                <!-- Similar table for completed applications -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
