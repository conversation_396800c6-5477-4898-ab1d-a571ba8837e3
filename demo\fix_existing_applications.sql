-- Fix existing applications to have proper default values for admin verification
-- This script updates existing applications to set admin_verified = FALSE where it's NULL

USE netcafe_db6;

-- Update existing applications to have admin_verified = FALSE (default state)
UPDATE scheme_applications 
SET admin_verified = FALSE 
WHERE admin_verified IS NULL;

-- Show current status after update
SELECT 
    COUNT(*) as total_applications,
    SUM(CASE WHEN admin_verified = TRUE THEN 1 ELSE 0 END) as verified_applications,
    SUM(CASE WHEN admin_verified = FALSE THEN 1 ELSE 0 END) as unverified_applications,
    SUM(CASE WHEN admin_verified IS NULL THEN 1 ELSE 0 END) as null_verification
FROM scheme_applications;

-- Show applications that need verification
SELECT 
    id,
    status,
    payment_status,
    admin_verified,
    application_date
FROM scheme_applications 
WHERE status = 'PENDING'
ORDER BY application_date DESC
LIMIT 5;
