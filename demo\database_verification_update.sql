-- Database update script to add admin verification fields to scheme_applications table
-- Run this script to update your existing database with the new verification functionality

-- Add admin verification columns to scheme_applications table
ALTER TABLE scheme_applications 
ADD COLUMN admin_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN admin_verification_date DATETIME NULL,
ADD COLUMN admin_verification_remarks TEXT NULL,
ADD COLUMN admin_verified_by BIGINT NULL;

-- Add foreign key constraint for admin_verified_by (optional, if you want referential integrity)
-- ALTER TABLE scheme_applications 
-- ADD CONSTRAINT fk_scheme_applications_admin_verified_by 
-- FOREIGN KEY (admin_verified_by) REFERENCES admins(id);

-- Update existing applications to have admin_verified = FALSE (already set by DEFAULT)
-- This ensures all existing applications will need admin verification before NetCafe claiming

-- Optional: If you want to automatically verify all existing applications, uncomment the following:
-- UPDATE scheme_applications SET admin_verified = TRUE WHERE admin_verified IS NULL;

-- Create an index on admin_verified for better query performance
CREATE INDEX idx_scheme_applications_admin_verified ON scheme_applications(admin_verified);

-- Create an index on admin_verification_date for better query performance
CREATE INDEX idx_scheme_applications_admin_verification_date ON scheme_applications(admin_verification_date);

-- Display current status
SELECT 
    COUNT(*) as total_applications,
    SUM(CASE WHEN admin_verified = TRUE THEN 1 ELSE 0 END) as verified_applications,
    SUM(CASE WHEN admin_verified = FALSE OR admin_verified IS NULL THEN 1 ELSE 0 END) as unverified_applications
FROM scheme_applications;

-- Show applications that need verification
SELECT 
    id,
    status,
    admin_verified,
    application_date,
    payment_status
FROM scheme_applications 
WHERE (admin_verified = FALSE OR admin_verified IS NULL) 
AND status = 'PENDING'
ORDER BY application_date DESC;
