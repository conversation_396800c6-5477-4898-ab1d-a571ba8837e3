package com.example.demo.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "application_field_values")
public class ApplicationFieldValue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ManyToOne
    @JoinColumn(name = "application_id", nullable = false)
    private SchemeApplication application;

    @ManyToOne
    @JoinColumn(name = "scheme_field_id", nullable = false)
    private SchemeField schemeField;

    @Column(columnDefinition = "TEXT")
    private String fieldValue;

    @Column(nullable = false)
    private LocalDateTime submissionDate;

    // Default constructor
    public ApplicationFieldValue() {
        this.submissionDate = LocalDateTime.now();
    }

    // Constructor with fields
    public ApplicationFieldValue(SchemeApplication application, SchemeField schemeField, String fieldValue) {
        this.application = application;
        this.schemeField = schemeField;
        this.fieldValue = fieldValue;
        this.submissionDate = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public SchemeApplication getApplication() {
        return application;
    }

    public void setApplication(SchemeApplication application) {
        this.application = application;
    }

    public SchemeField getSchemeField() {
        return schemeField;
    }

    public void setSchemeField(SchemeField schemeField) {
        this.schemeField = schemeField;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    public LocalDateTime getSubmissionDate() {
        return submissionDate;
    }

    public void setSubmissionDate(LocalDateTime submissionDate) {
        this.submissionDate = submissionDate;
    }
}
