package com.example.demo.repository;

import com.example.demo.model.Scheme;
import com.example.demo.model.SchemeField;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SchemeFieldRepository extends JpaRepository<SchemeField, Long> {
    
    List<SchemeField> findBySchemeAndActiveOrderByDisplayOrder(Scheme scheme, boolean active);
    
    List<SchemeField> findBySchemeIdAndActiveOrderByDisplayOrder(Long schemeId, boolean active);
    
    @Query("SELECT sf FROM SchemeField sf WHERE sf.scheme.id = :schemeId AND sf.active = true ORDER BY sf.displayOrder ASC")
    List<SchemeField> findActiveFieldsBySchemeId(@Param("schemeId") Long schemeId);
    
    long countBySchemeAndActive(Scheme scheme, boolean active);
    
    void deleteByScheme(Scheme scheme);
}
