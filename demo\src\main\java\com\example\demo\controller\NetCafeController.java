package com.example.demo.controller;

import com.example.demo.model.ApplicationDocument;
import com.example.demo.model.ApplicationHistory;
import com.example.demo.model.Document;
import com.example.demo.model.Message;
import com.example.demo.model.NetCafeApplication;
import com.example.demo.model.NetCafeUser;
import com.example.demo.model.PaymentRecord;
import com.example.demo.model.SettlementRequest;
import com.example.demo.model.SchemeApplication;
import com.example.demo.service.ApplicationHistoryService;
import com.example.demo.service.MessageService;
import com.example.demo.service.NetCafeApplicationService;
import com.example.demo.service.SettlementService;
import com.example.demo.service.NetCafeUserService;
import com.example.demo.service.PaymentService;
import com.example.demo.service.SchemeApplicationService;
import com.example.demo.service.SchemeService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.time.LocalDateTime;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import java.io.ByteArrayOutputStream;

@Controller
@RequestMapping("/netcafe")
public class NetCafeController {

    @Autowired
    private NetCafeUserService netCafeUserService;

    @Autowired
    private NetCafeApplicationService netCafeApplicationService;

    @Autowired
    private SchemeApplicationService schemeApplicationService;

    @Autowired
    private SchemeService schemeService;

    @Autowired
    private MessageService messageService;

    @Autowired
    private SettlementService settlementService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private ApplicationHistoryService applicationHistoryService;

    @GetMapping("/register")
    public String showRegistrationForm(Model model) {
        model.addAttribute("user", new NetCafeUser());
        return "netcafe/register";
    }

    @PostMapping("/register")
    public String registerUser(
            @ModelAttribute NetCafeUser user,
            @RequestParam("photoFile") MultipartFile photo,
            @RequestParam("cscCertificateFile") MultipartFile cscCertificate,
            @RequestParam("aadharCardPhotoFile") MultipartFile aadharCardPhoto,
            @RequestParam("panCardPhotoFile") MultipartFile panCardPhoto,
            RedirectAttributes redirectAttributes) {

        // Validate input
        if (netCafeUserService.isEmailTaken(user.getEmail())) {
            redirectAttributes.addFlashAttribute("error", "Email is already registered");
            return "redirect:/netcafe/register";
        }

        if (netCafeUserService.isAadharTaken(user.getAadharNumber())) {
            redirectAttributes.addFlashAttribute("error", "Aadhar number is already registered");
            return "redirect:/netcafe/register";
        }

        if (netCafeUserService.isPanTaken(user.getPanNumber())) {
            redirectAttributes.addFlashAttribute("error", "PAN number is already registered");
            return "redirect:/netcafe/register";
        }

        try {
            netCafeUserService.registerUser(user, photo, cscCertificate, aadharCardPhoto, panCardPhoto);
            redirectAttributes.addFlashAttribute("success", "Registration successful! Please wait for admin approval.");
            return "redirect:/netcafe/login";
        } catch (IOException e) {
            redirectAttributes.addFlashAttribute("error", "Error uploading files: " + e.getMessage());
            return "redirect:/netcafe/register";
        }
    }

    @GetMapping("/login")
    public String showLoginForm() {
        return "netcafe/login";
    }

    @PostMapping("/login")
    public String login(
            @RequestParam String email,
            @RequestParam String password,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        if (netCafeUserService.authenticateUser(email, password)) {
            Optional<NetCafeUser> userOpt = netCafeUserService.findByEmail(email);

            if (userOpt.isPresent()) {
                NetCafeUser user = userOpt.get();

                if (!user.isApproved()) {
                    redirectAttributes.addFlashAttribute("error", "Your account is pending approval");
                    return "redirect:/netcafe/login";
                }

                session.setAttribute("netCafeUser", user);
                return "redirect:/netcafe/dashboard";
            }
        }

        redirectAttributes.addFlashAttribute("error", "Invalid email or password");
        return "redirect:/netcafe/login";
    }

    @GetMapping("/dashboard")
    public String dashboard(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        // Get applications assigned to this NetCafe
        List<NetCafeApplication> pendingApplications = netCafeApplicationService.getPendingApplicationsByNetCafeUser(user);
        List<NetCafeApplication> processingApplications = netCafeApplicationService.getProcessingApplicationsByNetCafeUser(user);
        List<NetCafeApplication> completedApplications = netCafeApplicationService.getCompletedApplicationsByNetCafeUser(user);

        // Get available applications that can be claimed
        List<SchemeApplication> availableApplications = netCafeApplicationService.getAvailableApplications();

        // Get payment information
        BigDecimal unsettledAmount = paymentService.getTotalUnsettledAmountForNetCafe(user);

        // Calculate total earnings (sum of all completed payments)
        BigDecimal totalEarnings = BigDecimal.ZERO;
        List<PaymentRecord> allPayments = paymentService.getNetCafePaymentHistory(user);
        for (PaymentRecord payment : allPayments) {
            if ("COMPLETED".equals(payment.getStatus()) && payment.getAmount() != null) {
                totalEarnings = totalEarnings.add(payment.getAmount());
            }
        }

        // Get settlement information
        BigDecimal availableBalance = settlementService.calculateAvailableBalance(user);
        List<SettlementRequest> settlementRequests = settlementService.getSettlementRequestsByNetCafeUser(user);
        long pendingSettlements = settlementRequests.stream()
                .filter(request -> "PENDING".equals(request.getStatus()))
                .count();

        model.addAttribute("user", user);
        model.addAttribute("pendingApplications", pendingApplications);
        model.addAttribute("processingApplications", processingApplications);
        model.addAttribute("completedApplications", completedApplications);
        model.addAttribute("availableApplications", availableApplications);
        model.addAttribute("pendingCount", pendingApplications.size());
        model.addAttribute("processingCount", processingApplications.size());
        model.addAttribute("completedCount", completedApplications.size());
        model.addAttribute("availableCount", availableApplications.size());
        model.addAttribute("unsettledAmount", unsettledAmount);
        model.addAttribute("totalEarnings", totalEarnings);
        model.addAttribute("availableBalance", availableBalance);
        model.addAttribute("pendingSettlements", pendingSettlements);

        return "netcafe/dashboard";
    }

    @GetMapping("/applications")
    public String viewApplications(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        List<NetCafeApplication> applications = netCafeApplicationService.getApplicationsByNetCafeUser(user);

        model.addAttribute("user", user);
        model.addAttribute("applications", applications);

        return "netcafe/applications";
    }

    @GetMapping("/available-applications")
    public String viewAvailableApplications(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        // Get all available applications
        List<SchemeApplication> availableApplications = netCafeApplicationService.getAvailableApplications();

        model.addAttribute("user", user);
        model.addAttribute("availableApplications", availableApplications);

        return "netcafe/available-applications";
    }

    @PostMapping("/claim-application/{id}")
    public String claimApplication(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        try {
            netCafeApplicationService.claimApplication(user, id);
            redirectAttributes.addFlashAttribute("success", "Application claimed successfully");
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error claiming application: " + e.getMessage());
        }

        return "redirect:/netcafe/available-applications";
    }

    @GetMapping("/application/{id}")
    public String viewApplicationDetails(
            @PathVariable Long id,
            HttpSession session,
            Model model,
            RedirectAttributes redirectAttributes) {

        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        Optional<NetCafeApplication> applicationOpt = netCafeApplicationService.getApplicationById(id);

        if (applicationOpt.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Application not found");
            return "redirect:/netcafe/applications";
        }

        NetCafeApplication netCafeApplication = applicationOpt.get();

        // Verify that this application belongs to the current NetCafe user
        if (!netCafeApplication.getNetCafeUser().getId().equals(user.getId())) {
            redirectAttributes.addFlashAttribute("error", "You don't have permission to view this application");
            return "redirect:/netcafe/applications";
        }

        SchemeApplication application = netCafeApplication.getApplication();

        // Debug the application
        if (application == null) {
            System.out.println("DEBUG - NetCafe Application Details - Scheme Application is NULL");
            redirectAttributes.addFlashAttribute("error", "Scheme Application not found");
            return "redirect:/netcafe/applications";
        }

        // Debug logging
        System.out.println("DEBUG - NetCafe Application Details - NetCafe Application ID: " + netCafeApplication.getId());
        System.out.println("DEBUG - NetCafe Application Details - Scheme Application ID: " + application.getId());
        System.out.println("DEBUG - NetCafe Application Details - NetCafe User ID: " + user.getId());
        System.out.println("DEBUG - NetCafe Application Details - NetCafe User Name: " + user.getName());

        // Check if application has user
        if (application.getUser() != null) {
            System.out.println("DEBUG - NetCafe Application Details - Applicant User ID: " + application.getUser().getId());
            System.out.println("DEBUG - NetCafe Application Details - Applicant User Name: " + application.getUser().getName());
        } else {
            System.out.println("DEBUG - NetCafe Application Details - Applicant User is NULL");
            // This is a critical issue - let's try to fix it
            System.out.println("DEBUG - NetCafe Application Details - Attempting to find the user for this application");

            // Try to find the user from the database based on the application ID
            try {
                Optional<SchemeApplication> refreshedApp = schemeApplicationService.getApplicationById(application.getId());
                if (refreshedApp.isPresent() && refreshedApp.get().getUser() != null) {
                    application = refreshedApp.get();
                    System.out.println("DEBUG - NetCafe Application Details - Found user: " + application.getUser().getName());
                } else {
                    System.out.println("DEBUG - NetCafe Application Details - Could not find user for this application");
                }
            } catch (Exception e) {
                System.out.println("DEBUG - NetCafe Application Details - Error finding user: " + e.getMessage());
            }
        }

        // Get required documents for this scheme if scheme exists
        List<Document> requiredDocuments = new ArrayList<>();
        if (application.getScheme() != null) {
            requiredDocuments = schemeService.getRequiredDocumentsForScheme(application.getScheme().getId());
            System.out.println("DEBUG - NetCafe Application Details - Scheme ID: " + application.getScheme().getId());
            System.out.println("DEBUG - NetCafe Application Details - Scheme Name: " + application.getScheme().getName());
            System.out.println("DEBUG - NetCafe Application Details - Required Documents: " + requiredDocuments.size());
        } else {
            System.out.println("DEBUG - NetCafe Application Details - Scheme is NULL");
        }

        // Check if user has granted document access
        boolean hasDocumentAccess = application.getDocumentAccessGranted() != null && application.getDocumentAccessGranted();
        System.out.println("DEBUG - NetCafe Application Details - Document Access Granted: " + hasDocumentAccess);

        // Get messages regardless of document access status
        List<Message> messages = new ArrayList<>();
        messages = messageService.getMessagesByNetCafeApplication(netCafeApplication);

        // Mark all messages from user as read
        messageService.markAllAsRead(netCafeApplication, "NETCAFE");

        // Get application history if available
        Optional<ApplicationHistory> applicationHistoryOpt = applicationHistoryService.getApplicationHistoryByNetCafeApplicationId(netCafeApplication.getId());
        if (applicationHistoryOpt.isPresent()) {
            model.addAttribute("applicationHistory", applicationHistoryOpt.get());
        }

        model.addAttribute("user", user);
        model.addAttribute("netCafeUser", user); // Add NetCafe user explicitly
        model.addAttribute("netCafeApplication", netCafeApplication);
        model.addAttribute("application", application);
        model.addAttribute("requiredDocuments", requiredDocuments);
        model.addAttribute("hasDocumentAccess", hasDocumentAccess);
        model.addAttribute("messages", messages);
        model.addAttribute("newMessage", new Message());

        return "netcafe/application-details";
    }

    @PostMapping("/application/{id}/update-status")
    public String updateApplicationStatus(
            @PathVariable Long id,
            @RequestParam String status,
            @RequestParam(required = false) String remarks,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        try {
            // Get the NetCafe application
            Optional<NetCafeApplication> applicationOpt = netCafeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/netcafe/applications";
            }

            NetCafeApplication netCafeApplication = applicationOpt.get();

            // Verify that this application belongs to the current NetCafe user
            if (!netCafeApplication.getNetCafeUser().getId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to update this application");
                return "redirect:/netcafe/applications";
            }

            // Update the status
            netCafeApplicationService.updateStatus(id, status, remarks);

            // If status is COMPLETED, create a completion receipt and record commission
            if ("COMPLETED".equals(status)) {
                // Set completion date
                netCafeApplication.setCompletionDate(LocalDateTime.now());
                netCafeApplicationService.saveApplication(netCafeApplication);

                // Create application history and generate receipt
                // This will also record the NetCafe commission
                ApplicationHistory history = applicationHistoryService.createApplicationHistory(
                        netCafeApplication.getApplication(), netCafeApplication);

                redirectAttributes.addFlashAttribute("success",
                        "Application marked as completed. Receipt ID: " + history.getCompletionReceiptId());
            } else {
                redirectAttributes.addFlashAttribute("success", "Application status updated successfully");
            }
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error updating application status: " + e.getMessage());
        }

        return "redirect:/netcafe/application/" + id;
    }

    @PostMapping("/application/{id}/mark-ready-for-completion")
    public String markReadyForCompletion(
            @PathVariable Long id,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        try {
            // Get the NetCafe application
            Optional<NetCafeApplication> applicationOpt = netCafeApplicationService.getApplicationById(id);

            if (applicationOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/netcafe/applications";
            }

            NetCafeApplication netCafeApplication = applicationOpt.get();

            // Verify that this application belongs to the current NetCafe user
            if (!netCafeApplication.getNetCafeUser().getId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to update this application");
                return "redirect:/netcafe/applications";
            }

            // Check if application is in COMPLETED status
            if (!"COMPLETED".equals(netCafeApplication.getStatus())) {
                redirectAttributes.addFlashAttribute("error", "Application must be completed before marking ready for user confirmation");
                return "redirect:/netcafe/application/" + id;
            }

            // Mark as ready for completion confirmation
            netCafeApplication.setReadyForCompletion(true);
            netCafeApplication.setReadyForCompletionDate(LocalDateTime.now());
            netCafeApplicationService.saveApplication(netCafeApplication);

            // Send a message to the user notifying them
            String notificationMessage = "Your application has been completed and is ready for your confirmation. " +
                    "Please review the work and confirm completion to finalize the process.";
            messageService.sendMessage(netCafeApplication, "NETCAFE", notificationMessage);

            redirectAttributes.addFlashAttribute("success",
                "Application marked as ready for user confirmation. User has been notified.");

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error marking application ready: " + e.getMessage());
        }

        return "redirect:/netcafe/application/" + id;
    }

    @GetMapping("/application/{id}/download-documents")
    public ResponseEntity<byte[]> downloadAllDocuments(@PathVariable Long id, HttpSession session) {
        System.out.println("DEBUG - Download Documents - Starting download for NetCafe Application ID: " + id);

        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            System.out.println("DEBUG - Download Documents - User not logged in");
            return new ResponseEntity<>("User not logged in".getBytes(), HttpStatus.UNAUTHORIZED);
        }

        System.out.println("DEBUG - Download Documents - User: " + user.getName() + " (ID: " + user.getId() + ")");

        Optional<NetCafeApplication> applicationOpt = netCafeApplicationService.getApplicationById(id);

        if (applicationOpt.isEmpty()) {
            System.out.println("DEBUG - Download Documents - NetCafe Application not found with ID: " + id);
            return new ResponseEntity<>("Application not found".getBytes(), HttpStatus.NOT_FOUND);
        }

        NetCafeApplication netCafeApplication = applicationOpt.get();
        System.out.println("DEBUG - Download Documents - NetCafe Application found: " + netCafeApplication.getId());

        // Verify that this application belongs to the current NetCafe user
        if (!netCafeApplication.getNetCafeUser().getId().equals(user.getId())) {
            System.out.println("DEBUG - Download Documents - Permission denied. App NetCafe User ID: " +
                netCafeApplication.getNetCafeUser().getId() + ", Current User ID: " + user.getId());
            return new ResponseEntity<>("Permission denied".getBytes(), HttpStatus.FORBIDDEN);
        }

        // Check if user has granted document access
        SchemeApplication application = netCafeApplication.getApplication();

        if (application == null) {
            System.out.println("DEBUG - Download Documents - Scheme Application is null");
            return new ResponseEntity<>("Scheme Application not found".getBytes(), HttpStatus.NOT_FOUND);
        }

        System.out.println("DEBUG - Download Documents - Scheme Application ID: " + application.getId());

        boolean hasDocumentAccess = application.getDocumentAccessGranted() != null && application.getDocumentAccessGranted();
        System.out.println("DEBUG - Download Documents - Document Access Granted: " + hasDocumentAccess);

        if (!hasDocumentAccess) {
            System.out.println("DEBUG - Download Documents - Document access not granted");
            return new ResponseEntity<>("Document access not granted".getBytes(), HttpStatus.FORBIDDEN);
        }

        // Check if application has any documents (excluding payment-related documents)
        boolean hasMainDocument = application.getSupportingDocument() != null && application.getSupportingDocument().length > 0;
        boolean hasIndividualDocuments = application.getApplicationDocuments() != null && !application.getApplicationDocuments().isEmpty();
        // Payment screenshot is intentionally excluded from NetCafe downloads

        System.out.println("DEBUG - Download Documents - Has Main Document: " + hasMainDocument);
        System.out.println("DEBUG - Download Documents - Has Individual Documents: " + hasIndividualDocuments);
        System.out.println("DEBUG - Download Documents - Payment screenshots are excluded from NetCafe downloads");

        // If no documents, return a simple text file
        if (!hasMainDocument && !hasIndividualDocuments) {
            System.out.println("DEBUG - Download Documents - No documents available, returning text file");

            String message = "No documents are available for this application.\n\n" +
                             "Application ID: " + application.getId() + "\n" +
                             "User: " + (application.getUser() != null ? application.getUser().getName() : "Unknown") + "\n" +
                             "Status: " + application.getStatus() + "\n" +
                             "Date: " + LocalDateTime.now();

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.TEXT_PLAIN);
            headers.setContentDispositionFormData("attachment", "no_documents.txt");

            return new ResponseEntity<>(message.getBytes(), headers, HttpStatus.OK);
        }

        try {
            // Debug logging
            System.out.println("DEBUG - Download Documents - NetCafe Application ID: " + id);
            System.out.println("DEBUG - Download Documents - Scheme Application ID: " + application.getId());
            System.out.println("DEBUG - Download Documents - Document Access Granted: " + hasDocumentAccess);

            if (!hasMainDocument && !hasIndividualDocuments) {
                System.out.println("DEBUG - Download Documents - No documents available");
                return new ResponseEntity<>("No documents available".getBytes(), HttpStatus.NOT_FOUND);
            }

            // Get all documents as a ZIP file
            byte[] zipData = createDocumentsZip(application);

            if (zipData == null || zipData.length == 0) {
                System.out.println("DEBUG - Download Documents - ZIP data is empty");
                return new ResponseEntity<>("No documents available".getBytes(), HttpStatus.NOT_FOUND);
            }

            System.out.println("DEBUG - Download Documents - ZIP data size: " + zipData.length + " bytes");

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            headers.setContentDispositionFormData("attachment", "application_" + id + "_documents.zip");

            return new ResponseEntity<>(zipData, headers, HttpStatus.OK);
        } catch (Exception e) {
            System.out.println("DEBUG - Download Documents - Error: " + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<>(("Error: " + e.getMessage()).getBytes(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    private byte[] createDocumentsZip(SchemeApplication application) throws IOException {
        System.out.println("DEBUG - createDocumentsZip - Starting to create ZIP for application ID: " + application.getId());

        // Debug application documents
        if (application.getApplicationDocuments() != null) {
            System.out.println("DEBUG - createDocumentsZip - Application has " + application.getApplicationDocuments().size() + " documents in the collection");
            int index = 0;
            for (ApplicationDocument doc : application.getApplicationDocuments()) {
                index++;
                System.out.println("DEBUG - createDocumentsZip - Document " + index + ":");
                System.out.println("  ID: " + (doc.getId() != null ? doc.getId() : "null"));
                System.out.println("  Name: " + (doc.getDocumentName() != null ? doc.getDocumentName() : "null"));
                System.out.println("  Document: " + (doc.getDocument() != null ? "Present (ID: " + doc.getDocument().getId() + ")" : "null"));
                System.out.println("  File: " + (doc.getDocumentFile() != null ? doc.getDocumentFile().length + " bytes" : "null"));
            }
        } else {
            System.out.println("DEBUG - createDocumentsZip - Application documents collection is null");
        }

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ZipOutputStream zos = new ZipOutputStream(baos);

        boolean addedAnyDocument = false;

        // Add main supporting document if exists
        if (application.getSupportingDocument() != null && application.getSupportingDocument().length > 0) {
            try {
                System.out.println("DEBUG - createDocumentsZip - Adding supporting document, size: " + application.getSupportingDocument().length + " bytes");
                ZipEntry entry = new ZipEntry("supporting_document.pdf");
                zos.putNextEntry(entry);
                zos.write(application.getSupportingDocument());
                zos.closeEntry();
                addedAnyDocument = true;
            } catch (Exception e) {
                System.out.println("DEBUG - createDocumentsZip - Error adding supporting document: " + e.getMessage());
            }
        } else {
            System.out.println("DEBUG - createDocumentsZip - No supporting document available");
        }

        // Add individual documents if exist
        if (application.getApplicationDocuments() != null && !application.getApplicationDocuments().isEmpty()) {
            System.out.println("DEBUG - createDocumentsZip - Processing " + application.getApplicationDocuments().size() + " documents");
            int docCount = 0;
            int addedCount = 0;

            for (ApplicationDocument doc : application.getApplicationDocuments()) {
                docCount++;
                System.out.println("DEBUG - createDocumentsZip - Processing document " + docCount + " of " + application.getApplicationDocuments().size());

                if (doc == null) {
                    System.out.println("DEBUG - createDocumentsZip - Document object is null");
                    continue;
                }

                if (doc.getDocumentFile() != null && doc.getDocumentFile().length > 0) {
                    try {
                        String fileName = doc.getDocumentName();
                        if (fileName == null || fileName.isEmpty()) {
                            fileName = "document_" + doc.getId() + ".pdf";
                        }

                        // Make sure filename is unique by adding index if needed
                        fileName = "doc_" + docCount + "_" + fileName;

                        System.out.println("DEBUG - createDocumentsZip - Adding document: " + fileName + ", size: " + doc.getDocumentFile().length + " bytes");

                        ZipEntry entry = new ZipEntry(fileName);
                        zos.putNextEntry(entry);
                        zos.write(doc.getDocumentFile());
                        zos.closeEntry();
                        addedAnyDocument = true;
                        addedCount++;
                        System.out.println("DEBUG - createDocumentsZip - Successfully added document: " + fileName);
                    } catch (Exception e) {
                        System.out.println("DEBUG - createDocumentsZip - Error adding document: " + e.getMessage());
                        e.printStackTrace();
                    }
                } else {
                    System.out.println("DEBUG - createDocumentsZip - Document file is null or empty for document " + docCount);

                    // Try to load the document from the database if it's not loaded
                    if (doc.getDocument() != null && doc.getDocument().getId() != null) {
                        try {
                            System.out.println("DEBUG - createDocumentsZip - Attempting to load document from database, ID: " + doc.getDocument().getId());
                            // This is just a placeholder - we don't have direct access to document service here
                            // In a real implementation, you would load the document from the database
                            System.out.println("DEBUG - createDocumentsZip - Document loading from database not implemented");
                        } catch (Exception e) {
                            System.out.println("DEBUG - createDocumentsZip - Error loading document from database: " + e.getMessage());
                        }
                    }
                }
            }

            System.out.println("DEBUG - createDocumentsZip - Added " + addedCount + " out of " + docCount + " documents");
        } else {
            System.out.println("DEBUG - createDocumentsZip - No application documents available");
        }

        // Payment information is intentionally excluded from the documents shared with NetCafe users
        System.out.println("DEBUG - createDocumentsZip - Payment information and screenshots are excluded from NetCafe downloads");

        // Add a summary file with application information
        try {
            StringBuilder summary = new StringBuilder();
            summary.append("Application Summary\n");
            summary.append("===================\n\n");

            summary.append("Application ID: ").append(application.getId()).append("\n");
            summary.append("Status: ").append(application.getStatus() != null ? application.getStatus() : "Not set").append("\n");

            if (application.getUser() != null) {
                summary.append("\nApplicant Information:\n");
                summary.append("---------------------\n");
                summary.append("Name: ").append(application.getUser().getName() != null ? application.getUser().getName() : "Not provided").append("\n");
                summary.append("Email: ").append(application.getUser().getEmail() != null ? application.getUser().getEmail() : "Not provided").append("\n");
                summary.append("Mobile: ").append(application.getUser().getMobileNumber() != null ? application.getUser().getMobileNumber() : "Not provided").append("\n");
                summary.append("Address: ").append(application.getUser().getAddress() != null ? application.getUser().getAddress() : "Not provided").append("\n");
            }

            if (application.getScheme() != null) {
                summary.append("\nScheme Information:\n");
                summary.append("------------------\n");
                summary.append("Scheme ID: ").append(application.getScheme().getId()).append("\n");
                summary.append("Name: ").append(application.getScheme().getName() != null ? application.getScheme().getName() : "Not provided").append("\n");
                summary.append("Description: ").append(application.getScheme().getDescription() != null ? application.getScheme().getDescription() : "Not provided").append("\n");
                // Payment amount is intentionally excluded
            }

            // Payment information is intentionally excluded from the summary

            summary.append("\nDocument Information:\n");
            summary.append("--------------------\n");
            if (application.getSupportingDocument() != null && application.getSupportingDocument().length > 0) {
                summary.append("Main Supporting Document: Yes (").append(application.getSupportingDocument().length).append(" bytes)\n");
            } else {
                summary.append("Main Supporting Document: No\n");
            }

            if (application.getApplicationDocuments() != null && !application.getApplicationDocuments().isEmpty()) {
                summary.append("Individual Documents: ").append(application.getApplicationDocuments().size()).append(" documents\n");
                int index = 0;
                for (ApplicationDocument doc : application.getApplicationDocuments()) {
                    index++;
                    summary.append("  ").append(index).append(". ");
                    if (doc.getDocument() != null) {
                        summary.append(doc.getDocument().getName() != null ? doc.getDocument().getName() : "Unnamed Document");
                    } else {
                        summary.append("Unknown Document");
                    }
                    summary.append(" (").append(doc.getDocumentName() != null ? doc.getDocumentName() : "No filename").append(")");
                    if (doc.getDocumentFile() != null) {
                        summary.append(" - ").append(doc.getDocumentFile().length).append(" bytes");
                    } else {
                        summary.append(" - No file data");
                    }
                    summary.append("\n");
                }
            } else {
                summary.append("Individual Documents: None\n");
            }

            // Payment screenshot information is intentionally excluded

            summary.append("\nGenerated on: ").append(LocalDateTime.now()).append("\n");

            ZipEntry summaryEntry = new ZipEntry("application_summary.txt");
            zos.putNextEntry(summaryEntry);
            zos.write(summary.toString().getBytes());
            zos.closeEntry();

            System.out.println("DEBUG - createDocumentsZip - Added application summary");
            addedAnyDocument = true;
        } catch (Exception e) {
            System.out.println("DEBUG - createDocumentsZip - Error adding summary: " + e.getMessage());
            e.printStackTrace();
        }

        // If no documents were added, add a placeholder file
        if (!addedAnyDocument) {
            System.out.println("DEBUG - createDocumentsZip - No documents were added, adding placeholder");
            ZipEntry entry = new ZipEntry("no_documents.txt");
            zos.putNextEntry(entry);
            zos.write("No documents are available for this application.".getBytes());
            zos.closeEntry();
        }

        zos.close();
        byte[] result = baos.toByteArray();
        System.out.println("DEBUG - createDocumentsZip - Final ZIP size: " + result.length + " bytes");
        return result;
    }

    @GetMapping("/edit-profile")
    public String showEditProfileForm(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        // Refresh user data from database
        Optional<NetCafeUser> refreshedUser = netCafeUserService.findById(user.getId());
        if (refreshedUser.isPresent()) {
            user = refreshedUser.get();
            session.setAttribute("netCafeUser", user);
        }

        model.addAttribute("user", user);
        return "netcafe/edit-profile";
    }

    @PostMapping("/update-profile")
    public String updateProfile(
            @ModelAttribute NetCafeUser updatedUser,
            @RequestParam(required = false) MultipartFile photoFile,
            @RequestParam(required = false) MultipartFile cscCertificateFile,
            @RequestParam(required = false) MultipartFile aadharCardPhotoFile,
            @RequestParam(required = false) MultipartFile panCardPhotoFile,
            @RequestParam(required = false) String newPassword,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        NetCafeUser currentUser = (NetCafeUser) session.getAttribute("netCafeUser");

        if (currentUser == null) {
            return "redirect:/netcafe/login";
        }

        try {
            NetCafeUser updatedUserData = netCafeUserService.updateUserProfile(
                    currentUser.getId(),
                    updatedUser,
                    newPassword,
                    photoFile,
                    cscCertificateFile,
                    aadharCardPhotoFile,
                    panCardPhotoFile
            );

            session.setAttribute("netCafeUser", updatedUserData);
            redirectAttributes.addFlashAttribute("success", "Profile updated successfully");
        } catch (IOException e) {
            redirectAttributes.addFlashAttribute("error", "Error updating profile: " + e.getMessage());
        }

        return "redirect:/netcafe/edit-profile";
    }

    @PostMapping("/application/{id}/send-message")
    public String sendMessage(
            @PathVariable Long id,
            @RequestParam String content,
            @RequestParam(required = false) MultipartFile attachment,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        System.out.println("DEBUG - NetCafe Send Message - Starting with ID: " + id);
        System.out.println("DEBUG - NetCafe Send Message - Content: " + content);
        System.out.println("DEBUG - NetCafe Send Message - Has Attachment: " + (attachment != null && !attachment.isEmpty()));

        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            System.out.println("DEBUG - NetCafe Send Message - User not logged in");
            return "redirect:/netcafe/login";
        }

        System.out.println("DEBUG - NetCafe Send Message - User: " + user.getName() + " (ID: " + user.getId() + ")");

        try {
            // Get the NetCafe application
            System.out.println("DEBUG - NetCafe Send Message - Getting NetCafe application with ID: " + id);
            Optional<NetCafeApplication> netCafeApplicationOpt = netCafeApplicationService.getApplicationById(id);

            if (netCafeApplicationOpt.isEmpty()) {
                System.out.println("DEBUG - NetCafe Send Message - NetCafe application not found");
                redirectAttributes.addFlashAttribute("error", "Application not found");
                return "redirect:/netcafe/applications";
            }

            NetCafeApplication netCafeApplication = netCafeApplicationOpt.get();
            System.out.println("DEBUG - NetCafe Send Message - NetCafe application found: " + netCafeApplication.getId());

            // Verify that this application belongs to the current NetCafe user
            if (netCafeApplication.getNetCafeUser() == null) {
                System.out.println("DEBUG - NetCafe Send Message - NetCafe user is null in application");
                redirectAttributes.addFlashAttribute("error", "NetCafe user information is missing");
                return "redirect:/netcafe/applications";
            }

            if (!netCafeApplication.getNetCafeUser().getId().equals(user.getId())) {
                System.out.println("DEBUG - NetCafe Send Message - Permission denied. App NetCafe User ID: " +
                    netCafeApplication.getNetCafeUser().getId() + ", Current User ID: " + user.getId());
                redirectAttributes.addFlashAttribute("error", "You don't have permission to send messages for this application");
                return "redirect:/netcafe/applications";
            }

            // No need to check for document access anymore
            // We'll allow messaging as soon as the application is assigned to a NetCafe

            // Send the message with or without attachment
            if (attachment != null && !attachment.isEmpty()) {
                // Process the attachment
                String originalFilename = attachment.getOriginalFilename();
                String contentType = attachment.getContentType();
                long fileSize = attachment.getSize();
                byte[] fileBytes = attachment.getBytes();

                // Determine attachment type
                String attachmentType = "DOCUMENT";
                if (contentType != null) {
                    if (contentType.startsWith("image/")) {
                        attachmentType = "IMAGE";
                    } else if (contentType.equals("application/pdf")) {
                        attachmentType = "PDF";
                    }
                }

                System.out.println("DEBUG - NetCafe Send Message - Attachment Name: " + originalFilename);
                System.out.println("DEBUG - NetCafe Send Message - Attachment Type: " + attachmentType);
                System.out.println("DEBUG - NetCafe Send Message - Attachment Size: " + fileSize + " bytes");

                // Send message with attachment
                Message message = messageService.sendMessageWithAttachment(
                    netCafeApplication,
                    "NETCAFE",
                    content,
                    fileBytes,
                    originalFilename,
                    contentType,
                    fileSize,
                    attachmentType
                );

                System.out.println("DEBUG - NetCafe Send Message - Message with attachment sent with ID: " + message.getId());
            } else {
                // Send message without attachment
                System.out.println("DEBUG - NetCafe Send Message - Sending message without attachment");
                Message message = messageService.sendMessage(netCafeApplication, "NETCAFE", content);
                System.out.println("DEBUG - NetCafe Send Message - Message sent with ID: " + message.getId());
            }

            redirectAttributes.addFlashAttribute("success", "Message sent successfully");
            return "redirect:/netcafe/application/" + id;
        } catch (Exception e) {
            System.out.println("DEBUG - NetCafe Send Message - Error: " + e.getMessage());
            e.printStackTrace();
            redirectAttributes.addFlashAttribute("error", "Error sending message: " + e.getMessage());
            return "redirect:/netcafe/application/" + id;
        }
    }

    /**
     * Download message attachment
     */
    @GetMapping("/message-attachment/{id}")
    public ResponseEntity<byte[]> getMessageAttachment(@PathVariable Long id, HttpSession session) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        try {
            // Get the message
            Optional<Message> messageOpt = messageService.getMessageById(id);

            if (messageOpt.isEmpty() || !messageOpt.get().hasAttachment()) {
                return ResponseEntity.notFound().build();
            }

            Message message = messageOpt.get();
            NetCafeApplication netCafeApplication = message.getNetCafeApplication();

            // Check if the application belongs to the NetCafe user
            if (netCafeApplication.getNetCafeUser() == null ||
                !netCafeApplication.getNetCafeUser().getId().equals(user.getId())) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // Return the attachment
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType(message.getAttachmentContentType()));
            headers.setContentDispositionFormData("attachment", message.getAttachmentName());
            headers.setCacheControl("must-revalidate, post-check=0, pre-check=0");

            return new ResponseEntity<>(message.getAttachment(), headers, HttpStatus.OK);
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/application-history")
    public String viewApplicationHistory(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        // Get application history for NetCafe user
        List<ApplicationHistory> history = applicationHistoryService.getApplicationHistoryForNetCafeUser(user.getId());

        // Get broken bond applications
        List<ApplicationHistory> brokenBondHistory = applicationHistoryService.getBrokenBondApplicationsForNetCafeUser(user.getId());

        model.addAttribute("user", user);
        model.addAttribute("history", history);
        model.addAttribute("brokenBondHistory", brokenBondHistory);

        return "netcafe/application-history";
    }

    @GetMapping("/broken-bonds")
    public String viewBrokenBonds(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        // Get broken bond applications
        List<ApplicationHistory> brokenBondHistory = applicationHistoryService.getBrokenBondApplicationsForNetCafeUser(user.getId());

        model.addAttribute("user", user);
        model.addAttribute("brokenBondHistory", brokenBondHistory);

        return "netcafe/broken-bonds";
    }

    @GetMapping("/application/break-bond/{receiptId}")
    public String showBreakBondForm(@PathVariable String receiptId, HttpSession session, Model model, RedirectAttributes redirectAttributes) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        try {
            // Get application history by receipt ID
            Optional<ApplicationHistory> historyOpt = applicationHistoryService.getApplicationHistoryByReceiptId(receiptId);

            if (historyOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Invalid receipt ID");
                return "redirect:/netcafe/application-history";
            }

            ApplicationHistory history = historyOpt.get();

            // Verify that this application belongs to the current NetCafe user
            if (!history.getNetCafeUserId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to break this bond");
                return "redirect:/netcafe/application-history";
            }

            // Check if bond is already broken
            Optional<NetCafeApplication> netCafeAppOpt = netCafeApplicationService.getApplicationById(history.getNetCafeApplicationId());
            if (netCafeAppOpt.isPresent() && netCafeAppOpt.get().getBondBroken() != null && netCafeAppOpt.get().getBondBroken()) {
                redirectAttributes.addFlashAttribute("info", "This bond has already been broken");
                return "redirect:/netcafe/application-history";
            }

            model.addAttribute("history", history);
            return "netcafe/break-bond";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error loading application: " + e.getMessage());
            return "redirect:/netcafe/application-history";
        }
    }

    @PostMapping("/application/break-bond")
    public String breakBond(@RequestParam String receiptId, @RequestParam String reason, HttpSession session, RedirectAttributes redirectAttributes) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        try {
            // Get application history by receipt ID
            Optional<ApplicationHistory> historyOpt = applicationHistoryService.getApplicationHistoryByReceiptId(receiptId);

            if (historyOpt.isEmpty()) {
                redirectAttributes.addFlashAttribute("error", "Invalid receipt ID");
                return "redirect:/netcafe/application-history";
            }

            ApplicationHistory history = historyOpt.get();

            // Verify that this application belongs to the current NetCafe user
            if (!history.getNetCafeUserId().equals(user.getId())) {
                redirectAttributes.addFlashAttribute("error", "You don't have permission to break this bond");
                return "redirect:/netcafe/application-history";
            }

            // Check if bond is already broken
            Optional<NetCafeApplication> netCafeAppOpt = netCafeApplicationService.getApplicationById(history.getNetCafeApplicationId());
            if (netCafeAppOpt.isPresent() && netCafeAppOpt.get().getBondBroken() != null && netCafeAppOpt.get().getBondBroken()) {
                redirectAttributes.addFlashAttribute("info", "This bond has already been broken");
                return "redirect:/netcafe/application-history";
            }

            // Break the bond
            applicationHistoryService.breakBondByReceiptId(receiptId, reason, "NETCAFE");

            redirectAttributes.addFlashAttribute("success", "Bond broken successfully");
            return "redirect:/netcafe/application-history";
        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error breaking bond: " + e.getMessage());
            return "redirect:/netcafe/application-history";
        }
    }

    // Settlement-related methods
    @GetMapping("/settlement")
    public String showSettlementHistory(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        // Get available balance
        BigDecimal availableBalance = settlementService.calculateAvailableBalance(user);

        // Get settlement requests
        List<SettlementRequest> settlementRequests = settlementService.getSettlementRequestsByNetCafeUser(user);

        model.addAttribute("user", user);
        model.addAttribute("availableBalance", availableBalance);
        model.addAttribute("settlementRequests", settlementRequests);

        return "netcafe/settlement-history";
    }

    @GetMapping("/settlement/request")
    public String showSettlementRequestForm(HttpSession session, Model model) {
        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        // Get available balance
        BigDecimal availableBalance = settlementService.calculateAvailableBalance(user);

        model.addAttribute("user", user);
        model.addAttribute("availableBalance", availableBalance);

        return "netcafe/settlement-request";
    }

    @PostMapping("/settlement/request")
    public String submitSettlementRequest(
            @RequestParam BigDecimal requestedAmount,
            @RequestParam String paymentMethod,
            @RequestParam(required = false) String bankAccountNumber,
            @RequestParam(required = false) String ifscCode,
            @RequestParam(required = false) String bankName,
            @RequestParam(required = false) String accountHolderName,
            @RequestParam(required = false) String upiId,
            HttpSession session,
            RedirectAttributes redirectAttributes) {

        NetCafeUser user = (NetCafeUser) session.getAttribute("netCafeUser");

        if (user == null) {
            return "redirect:/netcafe/login";
        }

        try {
            // Validate minimum amount
            if (requestedAmount.compareTo(new BigDecimal("100")) < 0) {
                redirectAttributes.addFlashAttribute("error", "Minimum settlement amount is ₹100");
                return "redirect:/netcafe/settlement/request";
            }

            // Create settlement request
            SettlementRequest request = settlementService.createSettlementRequest(
                user, requestedAmount, paymentMethod, bankAccountNumber,
                ifscCode, bankName, accountHolderName, upiId
            );

            redirectAttributes.addFlashAttribute("success",
                "Settlement request submitted successfully. Request ID: " + request.getId());

        } catch (Exception e) {
            redirectAttributes.addFlashAttribute("error", "Error submitting settlement request: " + e.getMessage());
            return "redirect:/netcafe/settlement/request";
        }

        return "redirect:/netcafe/settlement";
    }

    @GetMapping("/logout")
    public String logout(HttpSession session) {
        session.removeAttribute("netCafeUser");
        return "redirect:/";
    }
}
